{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\Navbar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navbar as BootstrapNavbar, Nav, NavDropdown, Container } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n  return /*#__PURE__*/_jsxDEV(BootstrapNavbar, {\n    bg: \"dark\",\n    variant: \"dark\",\n    expand: \"lg\",\n    className: \"mb-4\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(BootstrapNavbar.Brand, {\n        href: \"/\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-print me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), \"Xerox Module\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BootstrapNavbar.Toggle, {\n        \"aria-controls\": \"basic-navbar-nav\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BootstrapNavbar.Collapse, {\n        id: \"basic-navbar-nav\",\n        children: [/*#__PURE__*/_jsxDEV(Nav, {\n          className: \"me-auto\",\n          children: user && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              href: \"/dashboard\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-tachometer-alt me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 19\n              }, this), \"Dashboard\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 17\n            }, this), user.userType === 'Student' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n                href: \"/upload\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-upload me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 36,\n                  columnNumber: 23\n                }, this), \"Upload Files\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n                href: \"/jobs\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-list me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 40,\n                  columnNumber: 23\n                }, this), \"My Jobs\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), user.userType === 'XeroxCenter' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n                href: \"/job-queue\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-tasks me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 49,\n                  columnNumber: 23\n                }, this), \"Job Queue\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n                href: \"/analytics\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-chart-bar me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 53,\n                  columnNumber: 23\n                }, this), \"Analytics\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 52,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav, {\n          children: user ? /*#__PURE__*/_jsxDEV(NavDropdown, {\n            title: /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-user me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 21\n              }, this), user.username]\n            }, void 0, true),\n            id: \"user-nav-dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n              href: \"/profile\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-user-edit me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 19\n              }, this), \"Profile\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n              href: \"/settings\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-cog me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 19\n              }, this), \"Settings\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n              onClick: handleLogout,\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-sign-out-alt me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 19\n              }, this), \"Logout\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n              href: \"/login\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-sign-in-alt me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 19\n              }, this), \"Login\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n              href: \"/register\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-user-plus me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 19\n              }, this), \"Register\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"owExUWFylk0vVlQUKU4QcBpCg7Y=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "BootstrapNavbar", "Nav", "NavDropdown", "Container", "useAuth", "useNavigate", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "_s", "user", "logout", "navigate", "handleLogout", "bg", "variant", "expand", "className", "children", "Brand", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Toggle", "Collapse", "id", "Link", "userType", "title", "username", "<PERSON><PERSON>", "Divider", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/Navbar.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navbar as BootstrapNavbar, Nav, NavDropdown, Container } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\n\nconst Navbar: React.FC = () => {\n  const { user, logout } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogout = () => {\n    logout();\n    navigate('/login');\n  };\n\n  return (\n    <BootstrapNavbar bg=\"dark\" variant=\"dark\" expand=\"lg\" className=\"mb-4\">\n      <Container>\n        <BootstrapNavbar.Brand href=\"/\">\n          <i className=\"fas fa-print me-2\"></i>\n          Xerox Module\n        </BootstrapNavbar.Brand>\n        \n        <BootstrapNavbar.Toggle aria-controls=\"basic-navbar-nav\" />\n        <BootstrapNavbar.Collapse id=\"basic-navbar-nav\">\n          <Nav className=\"me-auto\">\n            {user && (\n              <>\n                <Nav.Link href=\"/dashboard\">\n                  <i className=\"fas fa-tachometer-alt me-1\"></i>\n                  Dashboard\n                </Nav.Link>\n                \n                {user.userType === 'Student' && (\n                  <>\n                    <Nav.Link href=\"/upload\">\n                      <i className=\"fas fa-upload me-1\"></i>\n                      Upload Files\n                    </Nav.Link>\n                    <Nav.Link href=\"/jobs\">\n                      <i className=\"fas fa-list me-1\"></i>\n                      My Jobs\n                    </Nav.Link>\n                  </>\n                )}\n                \n                {user.userType === 'XeroxCenter' && (\n                  <>\n                    <Nav.Link href=\"/job-queue\">\n                      <i className=\"fas fa-tasks me-1\"></i>\n                      Job Queue\n                    </Nav.Link>\n                    <Nav.Link href=\"/analytics\">\n                      <i className=\"fas fa-chart-bar me-1\"></i>\n                      Analytics\n                    </Nav.Link>\n                  </>\n                )}\n              </>\n            )}\n          </Nav>\n          \n          <Nav>\n            {user ? (\n              <NavDropdown \n                title={\n                  <>\n                    <i className=\"fas fa-user me-1\"></i>\n                    {user.username}\n                  </>\n                } \n                id=\"user-nav-dropdown\"\n              >\n                <NavDropdown.Item href=\"/profile\">\n                  <i className=\"fas fa-user-edit me-2\"></i>\n                  Profile\n                </NavDropdown.Item>\n                <NavDropdown.Item href=\"/settings\">\n                  <i className=\"fas fa-cog me-2\"></i>\n                  Settings\n                </NavDropdown.Item>\n                <NavDropdown.Divider />\n                <NavDropdown.Item onClick={handleLogout}>\n                  <i className=\"fas fa-sign-out-alt me-2\"></i>\n                  Logout\n                </NavDropdown.Item>\n              </NavDropdown>\n            ) : (\n              <>\n                <Nav.Link href=\"/login\">\n                  <i className=\"fas fa-sign-in-alt me-1\"></i>\n                  Login\n                </Nav.Link>\n                <Nav.Link href=\"/register\">\n                  <i className=\"fas fa-user-plus me-1\"></i>\n                  Register\n                </Nav.Link>\n              </>\n            )}\n          </Nav>\n        </BootstrapNavbar.Collapse>\n      </Container>\n    </BootstrapNavbar>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,IAAIC,eAAe,EAAEC,GAAG,EAAEC,WAAW,EAAEC,SAAS,QAAQ,iBAAiB;AACxF,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/C,MAAMV,MAAgB,GAAGA,CAAA,KAAM;EAAAW,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGR,OAAO,CAAC,CAAC;EAClC,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,MAAMS,YAAY,GAAGA,CAAA,KAAM;IACzBF,MAAM,CAAC,CAAC;IACRC,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEN,OAAA,CAACP,eAAe;IAACe,EAAE,EAAC,MAAM;IAACC,OAAO,EAAC,MAAM;IAACC,MAAM,EAAC,IAAI;IAACC,SAAS,EAAC,MAAM;IAAAC,QAAA,eACpEZ,OAAA,CAACJ,SAAS;MAAAgB,QAAA,gBACRZ,OAAA,CAACP,eAAe,CAACoB,KAAK;QAACC,IAAI,EAAC,GAAG;QAAAF,QAAA,gBAC7BZ,OAAA;UAAGW,SAAS,EAAC;QAAmB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,gBAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAuB,CAAC,eAExBlB,OAAA,CAACP,eAAe,CAAC0B,MAAM;QAAC,iBAAc;MAAkB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3DlB,OAAA,CAACP,eAAe,CAAC2B,QAAQ;QAACC,EAAE,EAAC,kBAAkB;QAAAT,QAAA,gBAC7CZ,OAAA,CAACN,GAAG;UAACiB,SAAS,EAAC,SAAS;UAAAC,QAAA,EACrBR,IAAI,iBACHJ,OAAA,CAAAE,SAAA;YAAAU,QAAA,gBACEZ,OAAA,CAACN,GAAG,CAAC4B,IAAI;cAACR,IAAI,EAAC,YAAY;cAAAF,QAAA,gBACzBZ,OAAA;gBAAGW,SAAS,EAAC;cAA4B;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,aAEhD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,EAEVd,IAAI,CAACmB,QAAQ,KAAK,SAAS,iBAC1BvB,OAAA,CAAAE,SAAA;cAAAU,QAAA,gBACEZ,OAAA,CAACN,GAAG,CAAC4B,IAAI;gBAACR,IAAI,EAAC,SAAS;gBAAAF,QAAA,gBACtBZ,OAAA;kBAAGW,SAAS,EAAC;gBAAoB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,gBAExC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACXlB,OAAA,CAACN,GAAG,CAAC4B,IAAI;gBAACR,IAAI,EAAC,OAAO;gBAAAF,QAAA,gBACpBZ,OAAA;kBAAGW,SAAS,EAAC;gBAAkB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,WAEtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA,eACX,CACH,EAEAd,IAAI,CAACmB,QAAQ,KAAK,aAAa,iBAC9BvB,OAAA,CAAAE,SAAA;cAAAU,QAAA,gBACEZ,OAAA,CAACN,GAAG,CAAC4B,IAAI;gBAACR,IAAI,EAAC,YAAY;gBAAAF,QAAA,gBACzBZ,OAAA;kBAAGW,SAAS,EAAC;gBAAmB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,aAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACXlB,OAAA,CAACN,GAAG,CAAC4B,IAAI;gBAACR,IAAI,EAAC,YAAY;gBAAAF,QAAA,gBACzBZ,OAAA;kBAAGW,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,aAE3C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA,eACX,CACH;UAAA,eACD;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENlB,OAAA,CAACN,GAAG;UAAAkB,QAAA,EACDR,IAAI,gBACHJ,OAAA,CAACL,WAAW;YACV6B,KAAK,eACHxB,OAAA,CAAAE,SAAA;cAAAU,QAAA,gBACEZ,OAAA;gBAAGW,SAAS,EAAC;cAAkB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACnCd,IAAI,CAACqB,QAAQ;YAAA,eACd,CACH;YACDJ,EAAE,EAAC,mBAAmB;YAAAT,QAAA,gBAEtBZ,OAAA,CAACL,WAAW,CAAC+B,IAAI;cAACZ,IAAI,EAAC,UAAU;cAAAF,QAAA,gBAC/BZ,OAAA;gBAAGW,SAAS,EAAC;cAAuB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAkB,CAAC,eACnBlB,OAAA,CAACL,WAAW,CAAC+B,IAAI;cAACZ,IAAI,EAAC,WAAW;cAAAF,QAAA,gBAChCZ,OAAA;gBAAGW,SAAS,EAAC;cAAiB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,YAErC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAkB,CAAC,eACnBlB,OAAA,CAACL,WAAW,CAACgC,OAAO;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvBlB,OAAA,CAACL,WAAW,CAAC+B,IAAI;cAACE,OAAO,EAAErB,YAAa;cAAAK,QAAA,gBACtCZ,OAAA;gBAAGW,SAAS,EAAC;cAA0B;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,UAE9C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAkB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,gBAEdlB,OAAA,CAAAE,SAAA;YAAAU,QAAA,gBACEZ,OAAA,CAACN,GAAG,CAAC4B,IAAI;cAACR,IAAI,EAAC,QAAQ;cAAAF,QAAA,gBACrBZ,OAAA;gBAAGW,SAAS,EAAC;cAAyB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,SAE7C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACXlB,OAAA,CAACN,GAAG,CAAC4B,IAAI;cAACR,IAAI,EAAC,WAAW;cAAAF,QAAA,gBACxBZ,OAAA;gBAAGW,SAAS,EAAC;cAAuB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,YAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA,eACX;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACkB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAACf,EAAA,CAlGIX,MAAgB;EAAA,QACKK,OAAO,EACfC,WAAW;AAAA;AAAA+B,EAAA,GAFxBrC,MAAgB;AAoGtB,eAAeA,MAAM;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}