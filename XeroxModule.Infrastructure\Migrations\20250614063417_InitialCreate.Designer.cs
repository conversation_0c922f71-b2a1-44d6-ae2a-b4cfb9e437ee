﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using XeroxModule.Infrastructure.Data;

#nullable disable

namespace XeroxModule.Infrastructure.Migrations
{
    [DbContext(typeof(XeroxDbContext))]
    [Migration("20250614063417_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("XeroxModule.Core.Entities.FileUpload", b =>
                {
                    b.Property<int>("FileUploadID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FileUploadID"));

                    b.Property<string>("ColorType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("Copies")
                        .HasColumnType("int");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedUserID")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ModifiedUserID")
                        .HasColumnType("int");

                    b.Property<string>("OriginalFileName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("PaperSize")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int?>("PreferredXeroxCenterID")
                        .HasColumnType("int");

                    b.Property<string>("PrintType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("StudentID")
                        .HasColumnType("int");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("FileUploadID");

                    b.HasIndex("CreatedUserID");

                    b.HasIndex("ModifiedUserID");

                    b.HasIndex("PreferredXeroxCenterID");

                    b.HasIndex("StudentID");

                    b.ToTable("ZRX_FileUpload", (string)null);
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.Message", b =>
                {
                    b.Property<int>("MessageID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MessageID"));

                    b.Property<bool>("IsRead")
                        .HasColumnType("bit");

                    b.Property<string>("MessageText")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("PrintJobID")
                        .HasColumnType("int");

                    b.Property<int>("ReceiverUserID")
                        .HasColumnType("int");

                    b.Property<int>("SenderUserID")
                        .HasColumnType("int");

                    b.Property<DateTime>("SentAt")
                        .HasColumnType("datetime2");

                    b.HasKey("MessageID");

                    b.HasIndex("PrintJobID");

                    b.HasIndex("ReceiverUserID");

                    b.HasIndex("SenderUserID");

                    b.ToTable("ZRX_Message", (string)null);
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.Notification", b =>
                {
                    b.Property<int>("NotificationID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("NotificationID"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsRead")
                        .HasColumnType("bit");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("RelatedEntityID")
                        .HasColumnType("int");

                    b.Property<string>("RelatedEntityType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("UserID")
                        .HasColumnType("int");

                    b.HasKey("NotificationID");

                    b.HasIndex("UserID");

                    b.ToTable("ZRX_Notification", (string)null);
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.PrintJob", b =>
                {
                    b.Property<int>("PrintJobID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("PrintJobID"));

                    b.Property<DateTime?>("ActualCompletionTime")
                        .HasColumnType("datetime2");

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(10,2)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedUserID")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeliveredAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime?>("EstimatedCompletionTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("FileUploadID")
                        .HasColumnType("int");

                    b.Property<string>("JobNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ModifiedUserID")
                        .HasColumnType("int");

                    b.Property<string>("Priority")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("XeroxCenterID")
                        .HasColumnType("int");

                    b.HasKey("PrintJobID");

                    b.HasIndex("CreatedUserID");

                    b.HasIndex("FileUploadID");

                    b.HasIndex("JobNumber")
                        .IsUnique();

                    b.HasIndex("ModifiedUserID");

                    b.HasIndex("XeroxCenterID");

                    b.ToTable("ZRX_PrintJob", (string)null);
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.Rating", b =>
                {
                    b.Property<int>("RatingID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("RatingID"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("PrintJobID")
                        .HasColumnType("int");

                    b.Property<int>("RatingValue")
                        .HasColumnType("int");

                    b.Property<string>("Review")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("StudentID")
                        .HasColumnType("int");

                    b.Property<int>("XeroxCenterID")
                        .HasColumnType("int");

                    b.HasKey("RatingID");

                    b.HasIndex("StudentID");

                    b.HasIndex("XeroxCenterID");

                    b.HasIndex("PrintJobID", "StudentID")
                        .IsUnique();

                    b.ToTable("ZRX_Rating", (string)null);
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.Student", b =>
                {
                    b.Property<int>("StudentID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("StudentID"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Department")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("StudentNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("UserID")
                        .HasColumnType("int");

                    b.Property<int?>("Year")
                        .HasColumnType("int");

                    b.HasKey("StudentID");

                    b.HasIndex("StudentNumber")
                        .IsUnique();

                    b.HasIndex("UserID")
                        .IsUnique();

                    b.ToTable("STU_Student", (string)null);
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.User", b =>
                {
                    b.Property<int>("UserID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("UserID"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ModifiedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("UserType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("UserID");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("ZRX_User", (string)null);
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.XeroxCenter", b =>
                {
                    b.Property<int>("XeroxCenterID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("XeroxCenterID"));

                    b.Property<decimal?>("AverageRating")
                        .HasColumnType("decimal(3,2)");

                    b.Property<int>("CompletedJobs")
                        .HasColumnType("int");

                    b.Property<string>("ContactPerson")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("Created")
                        .HasColumnType("datetime2");

                    b.Property<int>("CreatedUserID")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("Modified")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ModifiedUserID")
                        .HasColumnType("int");

                    b.Property<int>("PendingJobs")
                        .HasColumnType("int");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("TotalJobs")
                        .HasColumnType("int");

                    b.Property<int>("TotalRatings")
                        .HasColumnType("int");

                    b.Property<int>("UserID")
                        .HasColumnType("int");

                    b.Property<string>("XeroxCenterName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("XeroxCenterID");

                    b.HasIndex("CreatedUserID");

                    b.HasIndex("ModifiedUserID");

                    b.HasIndex("UserID")
                        .IsUnique();

                    b.ToTable("ZRX_XeroxCenter", (string)null);
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.FileUpload", b =>
                {
                    b.HasOne("XeroxModule.Core.Entities.User", "CreatedUser")
                        .WithMany()
                        .HasForeignKey("CreatedUserID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("XeroxModule.Core.Entities.User", "ModifiedUser")
                        .WithMany()
                        .HasForeignKey("ModifiedUserID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("XeroxModule.Core.Entities.XeroxCenter", "PreferredXeroxCenter")
                        .WithMany("PreferredFileUploads")
                        .HasForeignKey("PreferredXeroxCenterID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("XeroxModule.Core.Entities.Student", "Student")
                        .WithMany("FileUploads")
                        .HasForeignKey("StudentID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedUser");

                    b.Navigation("ModifiedUser");

                    b.Navigation("PreferredXeroxCenter");

                    b.Navigation("Student");
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.Message", b =>
                {
                    b.HasOne("XeroxModule.Core.Entities.PrintJob", "PrintJob")
                        .WithMany("Messages")
                        .HasForeignKey("PrintJobID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("XeroxModule.Core.Entities.User", "ReceiverUser")
                        .WithMany("ReceivedMessages")
                        .HasForeignKey("ReceiverUserID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("XeroxModule.Core.Entities.User", "SenderUser")
                        .WithMany("SentMessages")
                        .HasForeignKey("SenderUserID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("PrintJob");

                    b.Navigation("ReceiverUser");

                    b.Navigation("SenderUser");
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.Notification", b =>
                {
                    b.HasOne("XeroxModule.Core.Entities.User", "User")
                        .WithMany("Notifications")
                        .HasForeignKey("UserID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.PrintJob", b =>
                {
                    b.HasOne("XeroxModule.Core.Entities.User", "CreatedUser")
                        .WithMany()
                        .HasForeignKey("CreatedUserID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("XeroxModule.Core.Entities.FileUpload", "FileUpload")
                        .WithMany("PrintJobs")
                        .HasForeignKey("FileUploadID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("XeroxModule.Core.Entities.User", "ModifiedUser")
                        .WithMany()
                        .HasForeignKey("ModifiedUserID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("XeroxModule.Core.Entities.XeroxCenter", "XeroxCenter")
                        .WithMany("PrintJobs")
                        .HasForeignKey("XeroxCenterID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CreatedUser");

                    b.Navigation("FileUpload");

                    b.Navigation("ModifiedUser");

                    b.Navigation("XeroxCenter");
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.Rating", b =>
                {
                    b.HasOne("XeroxModule.Core.Entities.PrintJob", "PrintJob")
                        .WithMany("Ratings")
                        .HasForeignKey("PrintJobID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("XeroxModule.Core.Entities.Student", "Student")
                        .WithMany("Ratings")
                        .HasForeignKey("StudentID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("XeroxModule.Core.Entities.XeroxCenter", "XeroxCenter")
                        .WithMany("Ratings")
                        .HasForeignKey("XeroxCenterID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("PrintJob");

                    b.Navigation("Student");

                    b.Navigation("XeroxCenter");
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.Student", b =>
                {
                    b.HasOne("XeroxModule.Core.Entities.User", "User")
                        .WithOne("Student")
                        .HasForeignKey("XeroxModule.Core.Entities.Student", "UserID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.XeroxCenter", b =>
                {
                    b.HasOne("XeroxModule.Core.Entities.User", "CreatedUser")
                        .WithMany()
                        .HasForeignKey("CreatedUserID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("XeroxModule.Core.Entities.User", "ModifiedUser")
                        .WithMany()
                        .HasForeignKey("ModifiedUserID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("XeroxModule.Core.Entities.User", "User")
                        .WithOne("XeroxCenter")
                        .HasForeignKey("XeroxModule.Core.Entities.XeroxCenter", "UserID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedUser");

                    b.Navigation("ModifiedUser");

                    b.Navigation("User");
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.FileUpload", b =>
                {
                    b.Navigation("PrintJobs");
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.PrintJob", b =>
                {
                    b.Navigation("Messages");

                    b.Navigation("Ratings");
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.Student", b =>
                {
                    b.Navigation("FileUploads");

                    b.Navigation("Ratings");
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.User", b =>
                {
                    b.Navigation("Notifications");

                    b.Navigation("ReceivedMessages");

                    b.Navigation("SentMessages");

                    b.Navigation("Student");

                    b.Navigation("XeroxCenter");
                });

            modelBuilder.Entity("XeroxModule.Core.Entities.XeroxCenter", b =>
                {
                    b.Navigation("PreferredFileUploads");

                    b.Navigation("PrintJobs");

                    b.Navigation("Ratings");
                });
#pragma warning restore 612, 618
        }
    }
}
