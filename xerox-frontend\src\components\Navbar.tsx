import React from 'react';
import { Navbar as BootstrapNavbar, Nav, NavDropdown, Container } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const Navbar: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <BootstrapNavbar bg="dark" variant="dark" expand="lg" className="mb-4">
      <Container>
        <BootstrapNavbar.Brand href="/">
          <i className="fas fa-print me-2"></i>
          Xerox Module
        </BootstrapNavbar.Brand>
        
        <BootstrapNavbar.Toggle aria-controls="basic-navbar-nav" />
        <BootstrapNavbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            {user && (
              <>
                <Nav.Link href="/dashboard">
                  <i className="fas fa-tachometer-alt me-1"></i>
                  Dashboard
                </Nav.Link>
                
                {user.userType === 'Student' && (
                  <>
                    <Nav.Link href="/upload">
                      <i className="fas fa-upload me-1"></i>
                      Upload Files
                    </Nav.Link>
                    <Nav.Link href="/jobs">
                      <i className="fas fa-list me-1"></i>
                      My Jobs
                    </Nav.Link>
                  </>
                )}
                
                {user.userType === 'XeroxCenter' && (
                  <>
                    <Nav.Link href="/job-queue">
                      <i className="fas fa-tasks me-1"></i>
                      Job Queue
                    </Nav.Link>
                    <Nav.Link href="/analytics">
                      <i className="fas fa-chart-bar me-1"></i>
                      Analytics
                    </Nav.Link>
                  </>
                )}
              </>
            )}
          </Nav>
          
          <Nav>
            {user ? (
              <NavDropdown 
                title={
                  <>
                    <i className="fas fa-user me-1"></i>
                    {user.username}
                  </>
                } 
                id="user-nav-dropdown"
              >
                <NavDropdown.Item href="/profile">
                  <i className="fas fa-user-edit me-2"></i>
                  Profile
                </NavDropdown.Item>
                <NavDropdown.Item href="/settings">
                  <i className="fas fa-cog me-2"></i>
                  Settings
                </NavDropdown.Item>
                <NavDropdown.Divider />
                <NavDropdown.Item onClick={handleLogout}>
                  <i className="fas fa-sign-out-alt me-2"></i>
                  Logout
                </NavDropdown.Item>
              </NavDropdown>
            ) : (
              <>
                <Nav.Link href="/login">
                  <i className="fas fa-sign-in-alt me-1"></i>
                  Login
                </Nav.Link>
                <Nav.Link href="/register">
                  <i className="fas fa-user-plus me-1"></i>
                  Register
                </Nav.Link>
              </>
            )}
          </Nav>
        </BootstrapNavbar.Collapse>
      </Container>
    </BootstrapNavbar>
  );
};

export default Navbar;
