{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\StudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Table, Badge, Alert, Form, Modal } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [xeroxCenters, setXeroxCenters] = useState([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n  useEffect(() => {\n    // Mock data - replace with actual API calls\n    setPrintJobs([{\n      id: 1,\n      jobNumber: 'JOB-001',\n      fileName: 'Assignment1.pdf',\n      status: 'InProgress',\n      cost: 25.50,\n      estimatedCompletionTime: '2024-01-15T14:00:00',\n      xeroxCenterName: 'Campus Copy Center',\n      created: '2024-01-15T10:00:00'\n    }, {\n      id: 2,\n      jobNumber: 'JOB-002',\n      fileName: 'Thesis_Chapter1.docx',\n      status: 'Completed',\n      cost: 45.00,\n      xeroxCenterName: 'Quick Print Shop',\n      created: '2024-01-14T09:00:00'\n    }]);\n    setXeroxCenters([{\n      id: 1,\n      name: 'Campus Copy Center',\n      location: 'Building A, Ground Floor',\n      pendingJobs: 5,\n      averageRating: 4.5,\n      isActive: true\n    }, {\n      id: 2,\n      name: 'Quick Print Shop',\n      location: 'Main Street, Shop 12',\n      pendingJobs: 12,\n      averageRating: 4.2,\n      isActive: true\n    }, {\n      id: 3,\n      name: 'Express Printing',\n      location: 'Library Building, 1st Floor',\n      pendingJobs: 3,\n      averageRating: 4.8,\n      isActive: true\n    }]);\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        variant: 'secondary',\n        icon: 'clock'\n      },\n      'UnderReview': {\n        variant: 'info',\n        icon: 'eye'\n      },\n      'Quoted': {\n        variant: 'warning',\n        icon: 'dollar-sign'\n      },\n      'WaitingConfirmation': {\n        variant: 'warning',\n        icon: 'hourglass-half'\n      },\n      'Confirmed': {\n        variant: 'primary',\n        icon: 'check'\n      },\n      'InProgress': {\n        variant: 'info',\n        icon: 'cog'\n      },\n      'Completed': {\n        variant: 'success',\n        icon: 'check-circle'\n      },\n      'Delivered': {\n        variant: 'success',\n        icon: 'truck'\n      },\n      'Rejected': {\n        variant: 'danger',\n        icon: 'times'\n      },\n      'Cancelled': {\n        variant: 'dark',\n        icon: 'ban'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'secondary',\n      icon: 'question'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `fas fa-${config.icon} me-1`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this);\n  };\n  const getWorkloadColor = pendingJobs => {\n    if (pendingJobs <= 5) return 'success';\n    if (pendingJobs <= 10) return 'warning';\n    return 'danger';\n  };\n  const handleFileUpload = () => {\n    // Mock file upload - replace with actual API call\n    console.log('Uploading file:', selectedFile, uploadData);\n    setShowUploadModal(false);\n    setSelectedFile(null);\n    setUploadData({\n      remarks: '',\n      preferredXeroxCenterId: '',\n      printType: 'Print',\n      copies: 1,\n      colorType: 'BlackWhite',\n      paperSize: 'A4'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-tachometer-alt me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), \"Student Dashboard\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: \"auto\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: () => setShowUploadModal(true),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), \"Upload Files\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-file-alt fa-2x text-primary mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Total Jobs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary\",\n              children: printJobs.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-clock fa-2x text-warning mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"In Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-warning\",\n              children: printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-check-circle fa-2x text-success mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Completed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-success\",\n              children: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-dollar-sign fa-2x text-info mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Total Spent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-info\",\n              children: [\"$\", printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-list me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this), \"Recent Print Jobs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: printJobs.length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n              responsive: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Job #\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"File Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Cost\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Xerox Center\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: printJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: job.jobNumber\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-file-pdf me-2 text-danger\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 27\n                    }, this), job.fileName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: getStatusBadge(job.status)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: job.xeroxCenterName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      className: \"me-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-eye\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 241,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 240,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-secondary\",\n                      size: \"sm\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-comment\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 244,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 25\n                  }, this)]\n                }, job.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"info\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-info-circle me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), \"No print jobs yet. Upload your first file to get started!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-store me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), \"Available Centers\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(Card, {\n              className: \"mb-3 border-0 bg-light\",\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-start mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: center.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: getWorkloadColor(center.pendingJobs),\n                    children: [center.pendingJobs, \" jobs\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted small mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-map-marker-alt me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 23\n                  }, this), center.location]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-star text-warning me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"small\",\n                      children: center.averageRating.toFixed(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-primary\",\n                    size: \"sm\",\n                    children: \"Select\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 19\n              }, this)\n            }, center.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showUploadModal,\n      onHide: () => setShowUploadModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), \"Upload Files for Printing\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Select File\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"file\",\n              accept: \".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\",\n              onChange: e => {\n                const files = e.target.files;\n                setSelectedFile(files ? files[0] : null);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Print Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.printType,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    printType: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Print\",\n                    children: \"Print\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Xerox\",\n                    children: \"Xerox\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Binding\",\n                    children: \"Binding\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Lamination\",\n                    children: \"Lamination\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Number of Copies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  min: \"1\",\n                  value: uploadData.copies,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    copies: parseInt(e.target.value)\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Color Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.colorType,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    colorType: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"BlackWhite\",\n                    children: \"Black & White\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Color\",\n                    children: \"Color\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Paper Size\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.paperSize,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    paperSize: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A4\",\n                    children: \"A4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A3\",\n                    children: \"A3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Letter\",\n                    children: \"Letter\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Legal\",\n                    children: \"Legal\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Preferred Xerox Center\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: uploadData.preferredXeroxCenterId,\n              onChange: e => setUploadData(prev => ({\n                ...prev,\n                preferredXeroxCenterId: e.target.value\n              })),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a center (optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 17\n              }, this), xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: center.id,\n                children: [center.name, \" - \", center.pendingJobs, \" pending jobs\"]\n              }, center.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Remarks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              placeholder: \"Any special instructions or remarks...\",\n              value: uploadData.remarks,\n              onChange: e => setUploadData(prev => ({\n                ...prev,\n                remarks: e.target.value\n              }))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowUploadModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleFileUpload,\n          disabled: !selectedFile,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), \"Upload File\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentDashboard, \"7ojXxjqihYdoyrPmwIm9juuCuWc=\", false, function () {\n  return [useAuth];\n});\n_c = StudentDashboard;\nexport default StudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Table", "Badge", "<PERSON><PERSON>", "Form", "Modal", "useAuth", "jsxDEV", "_jsxDEV", "StudentDashboard", "_s", "user", "printJobs", "setPrintJobs", "xeroxCenters", "setXeroxCenters", "showUploadModal", "setShowUploadModal", "selectedFile", "setSelectedFile", "uploadData", "setUploadData", "remarks", "preferredXeroxCenterId", "printType", "copies", "colorType", "paperSize", "id", "jobNumber", "fileName", "status", "cost", "estimatedCompletionTime", "xeroxCenterName", "created", "name", "location", "pendingJobs", "averageRating", "isActive", "getStatusBadge", "statusConfig", "variant", "icon", "config", "bg", "children", "className", "_jsxFileName", "lineNumber", "columnNumber", "getWorkloadColor", "handleFileUpload", "console", "log", "fluid", "username", "xs", "onClick", "md", "Body", "length", "filter", "job", "includes", "reduce", "sum", "toFixed", "lg", "Header", "responsive", "hover", "map", "size", "center", "show", "onHide", "closeButton", "Title", "Group", "Label", "Control", "type", "accept", "onChange", "e", "files", "target", "Text", "Select", "value", "prev", "min", "parseInt", "as", "rows", "placeholder", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/StudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, But<PERSON>, Table, Badge, Alert, Form, Modal } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  xeroxCenterName: string;\n  created: string;\n}\n\ninterface XeroxCenter {\n  id: number;\n  name: string;\n  location: string;\n  pendingJobs: number;\n  averageRating: number;\n  isActive: boolean;\n}\n\nconst StudentDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n\n  useEffect(() => {\n    // Mock data - replace with actual API calls\n    setPrintJobs([\n      {\n        id: 1,\n        jobNumber: 'JOB-001',\n        fileName: 'Assignment1.pdf',\n        status: 'InProgress',\n        cost: 25.50,\n        estimatedCompletionTime: '2024-01-15T14:00:00',\n        xeroxCenterName: 'Campus Copy Center',\n        created: '2024-01-15T10:00:00'\n      },\n      {\n        id: 2,\n        jobNumber: 'JOB-002',\n        fileName: 'Thesis_Chapter1.docx',\n        status: 'Completed',\n        cost: 45.00,\n        xeroxCenterName: 'Quick Print Shop',\n        created: '2024-01-14T09:00:00'\n      }\n    ]);\n\n    setXeroxCenters([\n      {\n        id: 1,\n        name: 'Campus Copy Center',\n        location: 'Building A, Ground Floor',\n        pendingJobs: 5,\n        averageRating: 4.5,\n        isActive: true\n      },\n      {\n        id: 2,\n        name: 'Quick Print Shop',\n        location: 'Main Street, Shop 12',\n        pendingJobs: 12,\n        averageRating: 4.2,\n        isActive: true\n      },\n      {\n        id: 3,\n        name: 'Express Printing',\n        location: 'Library Building, 1st Floor',\n        pendingJobs: 3,\n        averageRating: 4.8,\n        isActive: true\n      }\n    ]);\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { variant: 'secondary', icon: 'clock' },\n      'UnderReview': { variant: 'info', icon: 'eye' },\n      'Quoted': { variant: 'warning', icon: 'dollar-sign' },\n      'WaitingConfirmation': { variant: 'warning', icon: 'hourglass-half' },\n      'Confirmed': { variant: 'primary', icon: 'check' },\n      'InProgress': { variant: 'info', icon: 'cog' },\n      'Completed': { variant: 'success', icon: 'check-circle' },\n      'Delivered': { variant: 'success', icon: 'truck' },\n      'Rejected': { variant: 'danger', icon: 'times' },\n      'Cancelled': { variant: 'dark', icon: 'ban' }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'secondary', icon: 'question' };\n    \n    return (\n      <Badge bg={config.variant}>\n        <i className={`fas fa-${config.icon} me-1`}></i>\n        {status}\n      </Badge>\n    );\n  };\n\n  const getWorkloadColor = (pendingJobs: number) => {\n    if (pendingJobs <= 5) return 'success';\n    if (pendingJobs <= 10) return 'warning';\n    return 'danger';\n  };\n\n  const handleFileUpload = () => {\n    // Mock file upload - replace with actual API call\n    console.log('Uploading file:', selectedFile, uploadData);\n    setShowUploadModal(false);\n    setSelectedFile(null);\n    setUploadData({\n      remarks: '',\n      preferredXeroxCenterId: '',\n      printType: 'Print',\n      copies: 1,\n      colorType: 'BlackWhite',\n      paperSize: 'A4'\n    });\n  };\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-4\">\n        <Col>\n          <h2>\n            <i className=\"fas fa-tachometer-alt me-2\"></i>\n            Student Dashboard\n          </h2>\n          <p className=\"text-muted\">Welcome back, {user?.username}!</p>\n        </Col>\n        <Col xs=\"auto\">\n          <Button variant=\"primary\" onClick={() => setShowUploadModal(true)}>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload Files\n          </Button>\n        </Col>\n      </Row>\n\n      {/* Statistics Cards */}\n      <Row className=\"mb-4\">\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-file-alt fa-2x text-primary mb-2\"></i>\n              <h5>Total Jobs</h5>\n              <h3 className=\"text-primary\">{printJobs.length}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-clock fa-2x text-warning mb-2\"></i>\n              <h5>In Progress</h5>\n              <h3 className=\"text-warning\">\n                {printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length}\n              </h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-check-circle fa-2x text-success mb-2\"></i>\n              <h5>Completed</h5>\n              <h3 className=\"text-success\">\n                {printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length}\n              </h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-dollar-sign fa-2x text-info mb-2\"></i>\n              <h5>Total Spent</h5>\n              <h3 className=\"text-info\">\n                ${printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)}\n              </h3>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      <Row>\n        {/* Recent Jobs */}\n        <Col lg={8}>\n          <Card>\n            <Card.Header>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-list me-2\"></i>\n                Recent Print Jobs\n              </h5>\n            </Card.Header>\n            <Card.Body>\n              {printJobs.length > 0 ? (\n                <Table responsive hover>\n                  <thead>\n                    <tr>\n                      <th>Job #</th>\n                      <th>File Name</th>\n                      <th>Status</th>\n                      <th>Cost</th>\n                      <th>Xerox Center</th>\n                      <th>Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {printJobs.map(job => (\n                      <tr key={job.id}>\n                        <td>\n                          <strong>{job.jobNumber}</strong>\n                        </td>\n                        <td>\n                          <i className=\"fas fa-file-pdf me-2 text-danger\"></i>\n                          {job.fileName}\n                        </td>\n                        <td>{getStatusBadge(job.status)}</td>\n                        <td>\n                          {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                        </td>\n                        <td>{job.xeroxCenterName}</td>\n                        <td>\n                          <Button variant=\"outline-primary\" size=\"sm\" className=\"me-1\">\n                            <i className=\"fas fa-eye\"></i>\n                          </Button>\n                          <Button variant=\"outline-secondary\" size=\"sm\">\n                            <i className=\"fas fa-comment\"></i>\n                          </Button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </Table>\n              ) : (\n                <Alert variant=\"info\">\n                  <i className=\"fas fa-info-circle me-2\"></i>\n                  No print jobs yet. Upload your first file to get started!\n                </Alert>\n              )}\n            </Card.Body>\n          </Card>\n        </Col>\n\n        {/* Xerox Centers */}\n        <Col lg={4}>\n          <Card>\n            <Card.Header>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-store me-2\"></i>\n                Available Centers\n              </h5>\n            </Card.Header>\n            <Card.Body>\n              {xeroxCenters.map(center => (\n                <Card key={center.id} className=\"mb-3 border-0 bg-light\">\n                  <Card.Body className=\"p-3\">\n                    <div className=\"d-flex justify-content-between align-items-start mb-2\">\n                      <h6 className=\"mb-1\">{center.name}</h6>\n                      <Badge bg={getWorkloadColor(center.pendingJobs)}>\n                        {center.pendingJobs} jobs\n                      </Badge>\n                    </div>\n                    <p className=\"text-muted small mb-2\">\n                      <i className=\"fas fa-map-marker-alt me-1\"></i>\n                      {center.location}\n                    </p>\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                      <div>\n                        <i className=\"fas fa-star text-warning me-1\"></i>\n                        <span className=\"small\">{center.averageRating.toFixed(1)}</span>\n                      </div>\n                      <Button variant=\"outline-primary\" size=\"sm\">\n                        Select\n                      </Button>\n                    </div>\n                  </Card.Body>\n                </Card>\n              ))}\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Upload Modal */}\n      <Modal show={showUploadModal} onHide={() => setShowUploadModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload Files for Printing\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Form>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Select File</Form.Label>\n              <Form.Control\n                type=\"file\"\n                accept=\".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\"\n                onChange={(e) => {\n                  const files = (e.target as HTMLInputElement).files;\n                  setSelectedFile(files ? files[0] : null);\n                }}\n              />\n              <Form.Text className=\"text-muted\">\n                Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\n              </Form.Text>\n            </Form.Group>\n\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Print Type</Form.Label>\n                  <Form.Select\n                    value={uploadData.printType}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, printType: e.target.value }))}\n                  >\n                    <option value=\"Print\">Print</option>\n                    <option value=\"Xerox\">Xerox</option>\n                    <option value=\"Binding\">Binding</option>\n                    <option value=\"Lamination\">Lamination</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Number of Copies</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    min=\"1\"\n                    value={uploadData.copies}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, copies: parseInt(e.target.value) }))}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Color Type</Form.Label>\n                  <Form.Select\n                    value={uploadData.colorType}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, colorType: e.target.value }))}\n                  >\n                    <option value=\"BlackWhite\">Black & White</option>\n                    <option value=\"Color\">Color</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Paper Size</Form.Label>\n                  <Form.Select\n                    value={uploadData.paperSize}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, paperSize: e.target.value }))}\n                  >\n                    <option value=\"A4\">A4</option>\n                    <option value=\"A3\">A3</option>\n                    <option value=\"Letter\">Letter</option>\n                    <option value=\"Legal\">Legal</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Preferred Xerox Center</Form.Label>\n              <Form.Select\n                value={uploadData.preferredXeroxCenterId}\n                onChange={(e) => setUploadData(prev => ({ ...prev, preferredXeroxCenterId: e.target.value }))}\n              >\n                <option value=\"\">Select a center (optional)</option>\n                {xeroxCenters.map(center => (\n                  <option key={center.id} value={center.id}>\n                    {center.name} - {center.pendingJobs} pending jobs\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Remarks</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                placeholder=\"Any special instructions or remarks...\"\n                value={uploadData.remarks}\n                onChange={(e) => setUploadData(prev => ({ ...prev, remarks: e.target.value }))}\n              />\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowUploadModal(false)}>\n            Cancel\n          </Button>\n          <Button variant=\"primary\" onClick={handleFileUpload} disabled={!selectedFile}>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload File\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default StudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AACrG,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsBlD,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAK,CAAC,GAAGL,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC;IAC3C4B,OAAO,EAAE,EAAE;IACXC,sBAAsB,EAAE,EAAE;IAC1BC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFhC,SAAS,CAAC,MAAM;IACd;IACAkB,YAAY,CAAC,CACX;MACEe,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,iBAAiB;MAC3BC,MAAM,EAAE,YAAY;MACpBC,IAAI,EAAE,KAAK;MACXC,uBAAuB,EAAE,qBAAqB;MAC9CC,eAAe,EAAE,oBAAoB;MACrCC,OAAO,EAAE;IACX,CAAC,EACD;MACEP,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,sBAAsB;MAChCC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,KAAK;MACXE,eAAe,EAAE,kBAAkB;MACnCC,OAAO,EAAE;IACX,CAAC,CACF,CAAC;IAEFpB,eAAe,CAAC,CACd;MACEa,EAAE,EAAE,CAAC;MACLQ,IAAI,EAAE,oBAAoB;MAC1BC,QAAQ,EAAE,0BAA0B;MACpCC,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,GAAG;MAClBC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEZ,EAAE,EAAE,CAAC;MACLQ,IAAI,EAAE,kBAAkB;MACxBC,QAAQ,EAAE,sBAAsB;MAChCC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,GAAG;MAClBC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEZ,EAAE,EAAE,CAAC;MACLQ,IAAI,EAAE,kBAAkB;MACxBC,QAAQ,EAAE,6BAA6B;MACvCC,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,GAAG;MAClBC,QAAQ,EAAE;IACZ,CAAC,CACF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,cAAc,GAAIV,MAAc,IAAK;IACzC,MAAMW,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACpD,aAAa,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC/C,QAAQ,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAc,CAAC;MACrD,qBAAqB,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAiB,CAAC;MACrE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,YAAY,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC9C,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAe,CAAC;MACzD,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,UAAU,EAAE;QAAED,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAChD,WAAW,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC9C,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACX,MAAM,CAA8B,IAAI;MAAEY,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAW,CAAC;IAE9G,oBACEpC,OAAA,CAACN,KAAK;MAAC4C,EAAE,EAAED,MAAM,CAACF,OAAQ;MAAAI,QAAA,gBACxBvC,OAAA;QAAGwC,SAAS,EAAE,UAAUH,MAAM,CAACD,IAAI;MAAQ;QAAAd,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC/CpB,MAAM;IAAA;MAAAD,QAAA,EAAAmB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMC,gBAAgB,GAAId,WAAmB,IAAK;IAChD,IAAIA,WAAW,IAAI,CAAC,EAAE,OAAO,SAAS;IACtC,IAAIA,WAAW,IAAI,EAAE,EAAE,OAAO,SAAS;IACvC,OAAO,QAAQ;EACjB,CAAC;EAED,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACAC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAErC,YAAY,EAAEE,UAAU,CAAC;IACxDH,kBAAkB,CAAC,KAAK,CAAC;IACzBE,eAAe,CAAC,IAAI,CAAC;IACrBE,aAAa,CAAC;MACZC,OAAO,EAAE,EAAE;MACXC,sBAAsB,EAAE,EAAE;MAC1BC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE,YAAY;MACvBC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACEnB,OAAA,CAACZ,SAAS;IAAC4D,KAAK;IAAAT,QAAA,gBACdvC,OAAA,CAACX,GAAG;MAACmD,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBvC,OAAA,CAACV,GAAG;QAAAiD,QAAA,gBACFvC,OAAA;UAAAuC,QAAA,gBACEvC,OAAA;YAAGwC,SAAS,EAAC;UAA4B;YAAAlB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,qBAEhD;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3C,OAAA;UAAGwC,SAAS,EAAC,YAAY;UAAAD,QAAA,GAAC,gBAAc,EAACpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,QAAQ,EAAC,GAAC;QAAA;UAAA3B,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACN3C,OAAA,CAACV,GAAG;QAAC4D,EAAE,EAAC,MAAM;QAAAX,QAAA,eACZvC,OAAA,CAACR,MAAM;UAAC2C,OAAO,EAAC,SAAS;UAACgB,OAAO,EAAEA,CAAA,KAAM1C,kBAAkB,CAAC,IAAI,CAAE;UAAA8B,QAAA,gBAChEvC,OAAA;YAAGwC,SAAS,EAAC;UAAoB;YAAAlB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,gBAExC;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAArB,QAAA,EAAAmB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3C,OAAA,CAACX,GAAG;MAACmD,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBvC,OAAA,CAACV,GAAG;QAAC8D,EAAE,EAAE,CAAE;QAAAb,QAAA,eACTvC,OAAA,CAACT,IAAI;UAACiD,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3BvC,OAAA,CAACT,IAAI,CAAC8D,IAAI;YAAAd,QAAA,gBACRvC,OAAA;cAAGwC,SAAS,EAAC;YAAyC;cAAAlB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3D3C,OAAA;cAAAuC,QAAA,EAAI;YAAU;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnB3C,OAAA;cAAIwC,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAEnC,SAAS,CAACkD;YAAM;cAAAhC,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3C,OAAA,CAACV,GAAG;QAAC8D,EAAE,EAAE,CAAE;QAAAb,QAAA,eACTvC,OAAA,CAACT,IAAI;UAACiD,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3BvC,OAAA,CAACT,IAAI,CAAC8D,IAAI;YAAAd,QAAA,gBACRvC,OAAA;cAAGwC,SAAS,EAAC;YAAsC;cAAAlB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD3C,OAAA;cAAAuC,QAAA,EAAI;YAAW;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB3C,OAAA;cAAIwC,SAAS,EAAC,cAAc;cAAAD,QAAA,EACzBnC,SAAS,CAACmD,MAAM,CAACC,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,CAACC,QAAQ,CAACD,GAAG,CAACjC,MAAM,CAAC,CAAC,CAAC+B;YAAM;cAAAhC,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3C,OAAA,CAACV,GAAG;QAAC8D,EAAE,EAAE,CAAE;QAAAb,QAAA,eACTvC,OAAA,CAACT,IAAI;UAACiD,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3BvC,OAAA,CAACT,IAAI,CAAC8D,IAAI;YAAAd,QAAA,gBACRvC,OAAA;cAAGwC,SAAS,EAAC;YAA6C;cAAAlB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/D3C,OAAA;cAAAuC,QAAA,EAAI;YAAS;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClB3C,OAAA;cAAIwC,SAAS,EAAC,cAAc;cAAAD,QAAA,EACzBnC,SAAS,CAACmD,MAAM,CAACC,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACD,GAAG,CAACjC,MAAM,CAAC,CAAC,CAAC+B;YAAM;cAAAhC,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN3C,OAAA,CAACV,GAAG;QAAC8D,EAAE,EAAE,CAAE;QAAAb,QAAA,eACTvC,OAAA,CAACT,IAAI;UAACiD,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3BvC,OAAA,CAACT,IAAI,CAAC8D,IAAI;YAAAd,QAAA,gBACRvC,OAAA;cAAGwC,SAAS,EAAC;YAAyC;cAAAlB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3D3C,OAAA;cAAAuC,QAAA,EAAI;YAAW;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB3C,OAAA;cAAIwC,SAAS,EAAC,WAAW;cAAAD,QAAA,GAAC,GACvB,EAACnC,SAAS,CAACsD,MAAM,CAAC,CAACC,GAAG,EAAEH,GAAG,KAAKG,GAAG,IAAIH,GAAG,CAAChC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACoC,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAtC,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAArB,QAAA,EAAAmB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN3C,OAAA,CAACX,GAAG;MAAAkD,QAAA,gBAEFvC,OAAA,CAACV,GAAG;QAACuE,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACTvC,OAAA,CAACT,IAAI;UAAAgD,QAAA,gBACHvC,OAAA,CAACT,IAAI,CAACuE,MAAM;YAAAvB,QAAA,eACVvC,OAAA;cAAIwC,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAClBvC,OAAA;gBAAGwC,SAAS,EAAC;cAAkB;gBAAAlB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAEtC;YAAA;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACd3C,OAAA,CAACT,IAAI,CAAC8D,IAAI;YAAAd,QAAA,EACPnC,SAAS,CAACkD,MAAM,GAAG,CAAC,gBACnBtD,OAAA,CAACP,KAAK;cAACsE,UAAU;cAACC,KAAK;cAAAzB,QAAA,gBACrBvC,OAAA;gBAAAuC,QAAA,eACEvC,OAAA;kBAAAuC,QAAA,gBACEvC,OAAA;oBAAAuC,QAAA,EAAI;kBAAK;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACd3C,OAAA;oBAAAuC,QAAA,EAAI;kBAAS;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClB3C,OAAA;oBAAAuC,QAAA,EAAI;kBAAM;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf3C,OAAA;oBAAAuC,QAAA,EAAI;kBAAI;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACb3C,OAAA;oBAAAuC,QAAA,EAAI;kBAAY;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrB3C,OAAA;oBAAAuC,QAAA,EAAI;kBAAO;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAArB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR3C,OAAA;gBAAAuC,QAAA,EACGnC,SAAS,CAAC6D,GAAG,CAACT,GAAG,iBAChBxD,OAAA;kBAAAuC,QAAA,gBACEvC,OAAA;oBAAAuC,QAAA,eACEvC,OAAA;sBAAAuC,QAAA,EAASiB,GAAG,CAACnC;oBAAS;sBAAAC,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAArB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACL3C,OAAA;oBAAAuC,QAAA,gBACEvC,OAAA;sBAAGwC,SAAS,EAAC;oBAAkC;sBAAAlB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACnDa,GAAG,CAAClC,QAAQ;kBAAA;oBAAAA,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACL3C,OAAA;oBAAAuC,QAAA,EAAKN,cAAc,CAACuB,GAAG,CAACjC,MAAM;kBAAC;oBAAAD,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrC3C,OAAA;oBAAAuC,QAAA,EACGiB,GAAG,CAAChC,IAAI,GAAG,IAAIgC,GAAG,CAAChC,IAAI,CAACoC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;kBAAG;oBAAAtC,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACL3C,OAAA;oBAAAuC,QAAA,EAAKiB,GAAG,CAAC9B;kBAAe;oBAAAJ,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9B3C,OAAA;oBAAAuC,QAAA,gBACEvC,OAAA,CAACR,MAAM;sBAAC2C,OAAO,EAAC,iBAAiB;sBAAC+B,IAAI,EAAC,IAAI;sBAAC1B,SAAS,EAAC,MAAM;sBAAAD,QAAA,eAC1DvC,OAAA;wBAAGwC,SAAS,EAAC;sBAAY;wBAAAlB,QAAA,EAAAmB,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAArB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACT3C,OAAA,CAACR,MAAM;sBAAC2C,OAAO,EAAC,mBAAmB;sBAAC+B,IAAI,EAAC,IAAI;sBAAA3B,QAAA,eAC3CvC,OAAA;wBAAGwC,SAAS,EAAC;sBAAgB;wBAAAlB,QAAA,EAAAmB,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAArB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAArB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GApBEa,GAAG,CAACpC,EAAE;kBAAAE,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqBX,CACL;cAAC;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAER3C,OAAA,CAACL,KAAK;cAACwC,OAAO,EAAC,MAAM;cAAAI,QAAA,gBACnBvC,OAAA;gBAAGwC,SAAS,EAAC;cAAyB;gBAAAlB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,6DAE7C;YAAA;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UACR;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGN3C,OAAA,CAACV,GAAG;QAACuE,EAAE,EAAE,CAAE;QAAAtB,QAAA,eACTvC,OAAA,CAACT,IAAI;UAAAgD,QAAA,gBACHvC,OAAA,CAACT,IAAI,CAACuE,MAAM;YAAAvB,QAAA,eACVvC,OAAA;cAAIwC,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAClBvC,OAAA;gBAAGwC,SAAS,EAAC;cAAmB;gBAAAlB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAEvC;YAAA;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACd3C,OAAA,CAACT,IAAI,CAAC8D,IAAI;YAAAd,QAAA,EACPjC,YAAY,CAAC2D,GAAG,CAACE,MAAM,iBACtBnE,OAAA,CAACT,IAAI;cAAiBiD,SAAS,EAAC,wBAAwB;cAAAD,QAAA,eACtDvC,OAAA,CAACT,IAAI,CAAC8D,IAAI;gBAACb,SAAS,EAAC,KAAK;gBAAAD,QAAA,gBACxBvC,OAAA;kBAAKwC,SAAS,EAAC,uDAAuD;kBAAAD,QAAA,gBACpEvC,OAAA;oBAAIwC,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAE4B,MAAM,CAACvC;kBAAI;oBAAAN,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvC3C,OAAA,CAACN,KAAK;oBAAC4C,EAAE,EAAEM,gBAAgB,CAACuB,MAAM,CAACrC,WAAW,CAAE;oBAAAS,QAAA,GAC7C4B,MAAM,CAACrC,WAAW,EAAC,OACtB;kBAAA;oBAAAR,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAArB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN3C,OAAA;kBAAGwC,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,gBAClCvC,OAAA;oBAAGwC,SAAS,EAAC;kBAA4B;oBAAAlB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC7CwB,MAAM,CAACtC,QAAQ;gBAAA;kBAAAP,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACJ3C,OAAA;kBAAKwC,SAAS,EAAC,mDAAmD;kBAAAD,QAAA,gBAChEvC,OAAA;oBAAAuC,QAAA,gBACEvC,OAAA;sBAAGwC,SAAS,EAAC;oBAA+B;sBAAAlB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjD3C,OAAA;sBAAMwC,SAAS,EAAC,OAAO;sBAAAD,QAAA,EAAE4B,MAAM,CAACpC,aAAa,CAAC6B,OAAO,CAAC,CAAC;oBAAC;sBAAAtC,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAArB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACN3C,OAAA,CAACR,MAAM;oBAAC2C,OAAO,EAAC,iBAAiB;oBAAC+B,IAAI,EAAC,IAAI;oBAAA3B,QAAA,EAAC;kBAE5C;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAArB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GArBHwB,MAAM,CAAC/C,EAAE;cAAAE,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBd,CACP;UAAC;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAArB,QAAA,EAAAmB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3C,OAAA,CAACH,KAAK;MAACuE,IAAI,EAAE5D,eAAgB;MAAC6D,MAAM,EAAEA,CAAA,KAAM5D,kBAAkB,CAAC,KAAK,CAAE;MAACyD,IAAI,EAAC,IAAI;MAAA3B,QAAA,gBAC9EvC,OAAA,CAACH,KAAK,CAACiE,MAAM;QAACQ,WAAW;QAAA/B,QAAA,eACvBvC,OAAA,CAACH,KAAK,CAAC0E,KAAK;UAAAhC,QAAA,gBACVvC,OAAA;YAAGwC,SAAS,EAAC;UAAoB;YAAAlB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,6BAExC;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACf3C,OAAA,CAACH,KAAK,CAACwD,IAAI;QAAAd,QAAA,eACTvC,OAAA,CAACJ,IAAI;UAAA2C,QAAA,gBACHvC,OAAA,CAACJ,IAAI,CAAC4E,KAAK;YAAChC,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BvC,OAAA,CAACJ,IAAI,CAAC6E,KAAK;cAAAlC,QAAA,EAAC;YAAW;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpC3C,OAAA,CAACJ,IAAI,CAAC8E,OAAO;cACXC,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,2CAA2C;cAClDC,QAAQ,EAAGC,CAAC,IAAK;gBACf,MAAMC,KAAK,GAAID,CAAC,CAACE,MAAM,CAAsBD,KAAK;gBAClDpE,eAAe,CAACoE,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;cAC1C;YAAE;cAAAzD,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF3C,OAAA,CAACJ,IAAI,CAACqF,IAAI;cAACzC,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAElC;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEb3C,OAAA,CAACX,GAAG;YAAAkD,QAAA,gBACFvC,OAAA,CAACV,GAAG;cAAC8D,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTvC,OAAA,CAACJ,IAAI,CAAC4E,KAAK;gBAAChC,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BvC,OAAA,CAACJ,IAAI,CAAC6E,KAAK;kBAAAlC,QAAA,EAAC;gBAAU;kBAAAjB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnC3C,OAAA,CAACJ,IAAI,CAACsF,MAAM;kBACVC,KAAK,EAAEvE,UAAU,CAACI,SAAU;kBAC5B6D,QAAQ,EAAGC,CAAC,IAAKjE,aAAa,CAACuE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEpE,SAAS,EAAE8D,CAAC,CAACE,MAAM,CAACG;kBAAM,CAAC,CAAC,CAAE;kBAAA5C,QAAA,gBAEjFvC,OAAA;oBAAQmF,KAAK,EAAC,OAAO;oBAAA5C,QAAA,EAAC;kBAAK;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpC3C,OAAA;oBAAQmF,KAAK,EAAC,OAAO;oBAAA5C,QAAA,EAAC;kBAAK;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpC3C,OAAA;oBAAQmF,KAAK,EAAC,SAAS;oBAAA5C,QAAA,EAAC;kBAAO;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxC3C,OAAA;oBAAQmF,KAAK,EAAC,YAAY;oBAAA5C,QAAA,EAAC;kBAAU;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAArB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN3C,OAAA,CAACV,GAAG;cAAC8D,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTvC,OAAA,CAACJ,IAAI,CAAC4E,KAAK;gBAAChC,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BvC,OAAA,CAACJ,IAAI,CAAC6E,KAAK;kBAAAlC,QAAA,EAAC;gBAAgB;kBAAAjB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzC3C,OAAA,CAACJ,IAAI,CAAC8E,OAAO;kBACXC,IAAI,EAAC,QAAQ;kBACbU,GAAG,EAAC,GAAG;kBACPF,KAAK,EAAEvE,UAAU,CAACK,MAAO;kBACzB4D,QAAQ,EAAGC,CAAC,IAAKjE,aAAa,CAACuE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEnE,MAAM,EAAEqE,QAAQ,CAACR,CAAC,CAACE,MAAM,CAACG,KAAK;kBAAE,CAAC,CAAC;gBAAE;kBAAA7D,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC;cAAA;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3C,OAAA,CAACX,GAAG;YAAAkD,QAAA,gBACFvC,OAAA,CAACV,GAAG;cAAC8D,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTvC,OAAA,CAACJ,IAAI,CAAC4E,KAAK;gBAAChC,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BvC,OAAA,CAACJ,IAAI,CAAC6E,KAAK;kBAAAlC,QAAA,EAAC;gBAAU;kBAAAjB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnC3C,OAAA,CAACJ,IAAI,CAACsF,MAAM;kBACVC,KAAK,EAAEvE,UAAU,CAACM,SAAU;kBAC5B2D,QAAQ,EAAGC,CAAC,IAAKjE,aAAa,CAACuE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAElE,SAAS,EAAE4D,CAAC,CAACE,MAAM,CAACG;kBAAM,CAAC,CAAC,CAAE;kBAAA5C,QAAA,gBAEjFvC,OAAA;oBAAQmF,KAAK,EAAC,YAAY;oBAAA5C,QAAA,EAAC;kBAAa;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjD3C,OAAA;oBAAQmF,KAAK,EAAC,OAAO;oBAAA5C,QAAA,EAAC;kBAAK;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAArB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN3C,OAAA,CAACV,GAAG;cAAC8D,EAAE,EAAE,CAAE;cAAAb,QAAA,eACTvC,OAAA,CAACJ,IAAI,CAAC4E,KAAK;gBAAChC,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BvC,OAAA,CAACJ,IAAI,CAAC6E,KAAK;kBAAAlC,QAAA,EAAC;gBAAU;kBAAAjB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnC3C,OAAA,CAACJ,IAAI,CAACsF,MAAM;kBACVC,KAAK,EAAEvE,UAAU,CAACO,SAAU;kBAC5B0D,QAAQ,EAAGC,CAAC,IAAKjE,aAAa,CAACuE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjE,SAAS,EAAE2D,CAAC,CAACE,MAAM,CAACG;kBAAM,CAAC,CAAC,CAAE;kBAAA5C,QAAA,gBAEjFvC,OAAA;oBAAQmF,KAAK,EAAC,IAAI;oBAAA5C,QAAA,EAAC;kBAAE;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9B3C,OAAA;oBAAQmF,KAAK,EAAC,IAAI;oBAAA5C,QAAA,EAAC;kBAAE;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9B3C,OAAA;oBAAQmF,KAAK,EAAC,QAAQ;oBAAA5C,QAAA,EAAC;kBAAM;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC3C,OAAA;oBAAQmF,KAAK,EAAC,OAAO;oBAAA5C,QAAA,EAAC;kBAAK;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAArB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3C,OAAA,CAACJ,IAAI,CAAC4E,KAAK;YAAChC,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BvC,OAAA,CAACJ,IAAI,CAAC6E,KAAK;cAAAlC,QAAA,EAAC;YAAsB;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/C3C,OAAA,CAACJ,IAAI,CAACsF,MAAM;cACVC,KAAK,EAAEvE,UAAU,CAACG,sBAAuB;cACzC8D,QAAQ,EAAGC,CAAC,IAAKjE,aAAa,CAACuE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAErE,sBAAsB,EAAE+D,CAAC,CAACE,MAAM,CAACG;cAAM,CAAC,CAAC,CAAE;cAAA5C,QAAA,gBAE9FvC,OAAA;gBAAQmF,KAAK,EAAC,EAAE;gBAAA5C,QAAA,EAAC;cAA0B;gBAAAjB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnDrC,YAAY,CAAC2D,GAAG,CAACE,MAAM,iBACtBnE,OAAA;gBAAwBmF,KAAK,EAAEhB,MAAM,CAAC/C,EAAG;gBAAAmB,QAAA,GACtC4B,MAAM,CAACvC,IAAI,EAAC,KAAG,EAACuC,MAAM,CAACrC,WAAW,EAAC,eACtC;cAAA,GAFaqC,MAAM,CAAC/C,EAAE;gBAAAE,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd,CACT,CAAC;YAAA;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEb3C,OAAA,CAACJ,IAAI,CAAC4E,KAAK;YAAChC,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BvC,OAAA,CAACJ,IAAI,CAAC6E,KAAK;cAAAlC,QAAA,EAAC;YAAO;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChC3C,OAAA,CAACJ,IAAI,CAAC8E,OAAO;cACXa,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRC,WAAW,EAAC,wCAAwC;cACpDN,KAAK,EAAEvE,UAAU,CAACE,OAAQ;cAC1B+D,QAAQ,EAAGC,CAAC,IAAKjE,aAAa,CAACuE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEtE,OAAO,EAAEgE,CAAC,CAACE,MAAM,CAACG;cAAM,CAAC,CAAC;YAAE;cAAA7D,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACb3C,OAAA,CAACH,KAAK,CAAC6F,MAAM;QAAAnD,QAAA,gBACXvC,OAAA,CAACR,MAAM;UAAC2C,OAAO,EAAC,WAAW;UAACgB,OAAO,EAAEA,CAAA,KAAM1C,kBAAkB,CAAC,KAAK,CAAE;UAAA8B,QAAA,EAAC;QAEtE;UAAAjB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3C,OAAA,CAACR,MAAM;UAAC2C,OAAO,EAAC,SAAS;UAACgB,OAAO,EAAEN,gBAAiB;UAAC8C,QAAQ,EAAE,CAACjF,YAAa;UAAA6B,QAAA,gBAC3EvC,OAAA;YAAGwC,SAAS,EAAC;UAAoB;YAAAlB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAExC;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAArB,QAAA,EAAAmB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAArB,QAAA,EAAAmB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACzC,EAAA,CA7YID,gBAA0B;EAAA,QACbH,OAAO;AAAA;AAAA8F,EAAA,GADpB3F,gBAA0B;AA+YhC,eAAeA,gBAAgB;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}