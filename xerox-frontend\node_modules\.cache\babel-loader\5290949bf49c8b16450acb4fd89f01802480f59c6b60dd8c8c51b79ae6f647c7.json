{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "useBootstrapBreakpoints", "useBootstrapMinBreakpoint", "jsx", "_jsx", "useCol", "as", "bsPrefix", "className", "props", "breakpoints", "minBreakpoint", "spans", "classes", "for<PERSON>ach", "brkPoint", "propValue", "span", "offset", "order", "infix", "push", "Col", "forwardRef", "ref", "colProps", "Component", "length", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/Col.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function useCol({\n  as,\n  bsPrefix,\n  className,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'col');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  const spans = [];\n  const classes = [];\n  breakpoints.forEach(brkPoint => {\n    const propValue = props[brkPoint];\n    delete props[brkPoint];\n    let span;\n    let offset;\n    let order;\n    if (typeof propValue === 'object' && propValue != null) {\n      ({\n        span,\n        offset,\n        order\n      } = propValue);\n    } else {\n      span = propValue;\n    }\n    const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n    if (span) spans.push(span === true ? `${bsPrefix}${infix}` : `${bsPrefix}${infix}-${span}`);\n    if (order != null) classes.push(`order${infix}-${order}`);\n    if (offset != null) classes.push(`offset${infix}-${offset}`);\n  });\n  return [{\n    ...props,\n    className: classNames(className, ...spans, ...classes)\n  }, {\n    as,\n    bsPrefix,\n    spans\n  }];\n}\nconst Col = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n(props, ref) => {\n  const [{\n    className,\n    ...colProps\n  }, {\n    as: Component = 'div',\n    bsPrefix,\n    spans\n  }] = useCol(props);\n  return /*#__PURE__*/_jsx(Component, {\n    ...colProps,\n    ref: ref,\n    className: classNames(className, !spans.length && bsPrefix)\n  });\n});\nCol.displayName = 'Col';\nexport default Col;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,EAAEC,uBAAuB,EAAEC,yBAAyB,QAAQ,iBAAiB;AACxG,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,MAAMA,CAAC;EACrBC,EAAE;EACFC,QAAQ;EACRC,SAAS;EACT,GAAGC;AACL,CAAC,EAAE;EACDF,QAAQ,GAAGP,kBAAkB,CAACO,QAAQ,EAAE,KAAK,CAAC;EAC9C,MAAMG,WAAW,GAAGT,uBAAuB,CAAC,CAAC;EAC7C,MAAMU,aAAa,GAAGT,yBAAyB,CAAC,CAAC;EACjD,MAAMU,KAAK,GAAG,EAAE;EAChB,MAAMC,OAAO,GAAG,EAAE;EAClBH,WAAW,CAACI,OAAO,CAACC,QAAQ,IAAI;IAC9B,MAAMC,SAAS,GAAGP,KAAK,CAACM,QAAQ,CAAC;IACjC,OAAON,KAAK,CAACM,QAAQ,CAAC;IACtB,IAAIE,IAAI;IACR,IAAIC,MAAM;IACV,IAAIC,KAAK;IACT,IAAI,OAAOH,SAAS,KAAK,QAAQ,IAAIA,SAAS,IAAI,IAAI,EAAE;MACtD,CAAC;QACCC,IAAI;QACJC,MAAM;QACNC;MACF,CAAC,GAAGH,SAAS;IACf,CAAC,MAAM;MACLC,IAAI,GAAGD,SAAS;IAClB;IACA,MAAMI,KAAK,GAAGL,QAAQ,KAAKJ,aAAa,GAAG,IAAII,QAAQ,EAAE,GAAG,EAAE;IAC9D,IAAIE,IAAI,EAAEL,KAAK,CAACS,IAAI,CAACJ,IAAI,KAAK,IAAI,GAAG,GAAGV,QAAQ,GAAGa,KAAK,EAAE,GAAG,GAAGb,QAAQ,GAAGa,KAAK,IAAIH,IAAI,EAAE,CAAC;IAC3F,IAAIE,KAAK,IAAI,IAAI,EAAEN,OAAO,CAACQ,IAAI,CAAC,QAAQD,KAAK,IAAID,KAAK,EAAE,CAAC;IACzD,IAAID,MAAM,IAAI,IAAI,EAAEL,OAAO,CAACQ,IAAI,CAAC,SAASD,KAAK,IAAIF,MAAM,EAAE,CAAC;EAC9D,CAAC,CAAC;EACF,OAAO,CAAC;IACN,GAAGT,KAAK;IACRD,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAE,GAAGI,KAAK,EAAE,GAAGC,OAAO;EACvD,CAAC,EAAE;IACDP,EAAE;IACFC,QAAQ;IACRK;EACF,CAAC,CAAC;AACJ;AACA,MAAMU,GAAG,GAAG,aAAavB,KAAK,CAACwB,UAAU;AACzC;AACA,CAACd,KAAK,EAAEe,GAAG,KAAK;EACd,MAAM,CAAC;IACLhB,SAAS;IACT,GAAGiB;EACL,CAAC,EAAE;IACDnB,EAAE,EAAEoB,SAAS,GAAG,KAAK;IACrBnB,QAAQ;IACRK;EACF,CAAC,CAAC,GAAGP,MAAM,CAACI,KAAK,CAAC;EAClB,OAAO,aAAaL,IAAI,CAACsB,SAAS,EAAE;IAClC,GAAGD,QAAQ;IACXD,GAAG,EAAEA,GAAG;IACRhB,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAE,CAACI,KAAK,CAACe,MAAM,IAAIpB,QAAQ;EAC5D,CAAC,CAAC;AACJ,CAAC,CAAC;AACFe,GAAG,CAACM,WAAW,GAAG,KAAK;AACvB,eAAeN,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}