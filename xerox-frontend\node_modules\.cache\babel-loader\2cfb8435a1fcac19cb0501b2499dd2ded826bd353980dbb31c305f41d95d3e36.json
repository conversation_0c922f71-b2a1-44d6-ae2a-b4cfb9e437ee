{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormCheckInput from './FormCheckInput';\nimport InputGroupContext from './InputGroupContext';\nimport InputGroupText from './InputGroupText';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupCheckbox = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"checkbox\",\n    ...props\n  })\n});\nconst InputGroupRadio = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"radio\",\n    ...props\n  })\n});\nconst InputGroup = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  hasValidation,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group');\n\n  // Intentionally an empty object. Used in detecting if a dropdown\n  // exists under an input group.\n  const contextValue = useMemo(() => ({}), []);\n  return /*#__PURE__*/_jsx(InputGroupContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix, size && `${bsPrefix}-${size}`, hasValidation && 'has-validation')\n    })\n  });\n});\nInputGroup.displayName = 'InputGroup';\nexport default Object.assign(InputGroup, {\n  Text: InputGroupText,\n  Radio: InputGroupRadio,\n  Checkbox: InputGroupCheckbox\n});", "map": {"version": 3, "names": ["classNames", "React", "useMemo", "useBootstrapPrefix", "FormCheckInput", "InputGroupContext", "InputGroupText", "jsx", "_jsx", "InputGroupCheckbox", "props", "children", "type", "InputGroupRadio", "InputGroup", "forwardRef", "bsPrefix", "size", "hasValidation", "className", "as", "Component", "ref", "contextValue", "Provider", "value", "displayName", "Object", "assign", "Text", "Radio", "Checkbox"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/InputGroup.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormCheckInput from './FormCheckInput';\nimport InputGroupContext from './InputGroupContext';\nimport InputGroupText from './InputGroupText';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst InputGroupCheckbox = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"checkbox\",\n    ...props\n  })\n});\nconst InputGroupRadio = props => /*#__PURE__*/_jsx(InputGroupText, {\n  children: /*#__PURE__*/_jsx(FormCheckInput, {\n    type: \"radio\",\n    ...props\n  })\n});\nconst InputGroup = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  size,\n  hasValidation,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'input-group');\n\n  // Intentionally an empty object. Used in detecting if a dropdown\n  // exists under an input group.\n  const contextValue = useMemo(() => ({}), []);\n  return /*#__PURE__*/_jsx(InputGroupContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix, size && `${bsPrefix}-${size}`, hasValidation && 'has-validation')\n    })\n  });\n});\nInputGroup.displayName = 'InputGroup';\nexport default Object.assign(InputGroup, {\n  Text: InputGroupText,\n  Radio: InputGroupRadio,\n  Checkbox: InputGroupCheckbox\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,kBAAkB,GAAGC,KAAK,IAAI,aAAaF,IAAI,CAACF,cAAc,EAAE;EACpEK,QAAQ,EAAE,aAAaH,IAAI,CAACJ,cAAc,EAAE;IAC1CQ,IAAI,EAAE,UAAU;IAChB,GAAGF;EACL,CAAC;AACH,CAAC,CAAC;AACF,MAAMG,eAAe,GAAGH,KAAK,IAAI,aAAaF,IAAI,CAACF,cAAc,EAAE;EACjEK,QAAQ,EAAE,aAAaH,IAAI,CAACJ,cAAc,EAAE;IAC1CQ,IAAI,EAAE,OAAO;IACb,GAAGF;EACL,CAAC;AACH,CAAC,CAAC;AACF,MAAMI,UAAU,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,CAAC;EAChDC,QAAQ;EACRC,IAAI;EACJC,aAAa;EACbC,SAAS;EACT;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrB,GAAGX;AACL,CAAC,EAAEY,GAAG,KAAK;EACTN,QAAQ,GAAGb,kBAAkB,CAACa,QAAQ,EAAE,aAAa,CAAC;;EAEtD;EACA;EACA,MAAMO,YAAY,GAAGrB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EAC5C,OAAO,aAAaM,IAAI,CAACH,iBAAiB,CAACmB,QAAQ,EAAE;IACnDC,KAAK,EAAEF,YAAY;IACnBZ,QAAQ,EAAE,aAAaH,IAAI,CAACa,SAAS,EAAE;MACrCC,GAAG,EAAEA,GAAG;MACR,GAAGZ,KAAK;MACRS,SAAS,EAAEnB,UAAU,CAACmB,SAAS,EAAEH,QAAQ,EAAEC,IAAI,IAAI,GAAGD,QAAQ,IAAIC,IAAI,EAAE,EAAEC,aAAa,IAAI,gBAAgB;IAC7G,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,UAAU,CAACY,WAAW,GAAG,YAAY;AACrC,eAAeC,MAAM,CAACC,MAAM,CAACd,UAAU,EAAE;EACvCe,IAAI,EAAEvB,cAAc;EACpBwB,KAAK,EAAEjB,eAAe;EACtBkB,QAAQ,EAAEtB;AACZ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}