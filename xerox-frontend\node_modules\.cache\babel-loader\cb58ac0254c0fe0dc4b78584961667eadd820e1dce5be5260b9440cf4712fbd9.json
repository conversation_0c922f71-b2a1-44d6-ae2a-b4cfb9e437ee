{"ast": null, "code": "import * as React from 'react';\nconst DropdownContext = /*#__PURE__*/React.createContext(null);\nexport default DropdownContext;", "map": {"version": 3, "names": ["React", "DropdownContext", "createContext"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@restart/ui/esm/DropdownContext.js"], "sourcesContent": ["import * as React from 'react';\nconst DropdownContext = /*#__PURE__*/React.createContext(null);\nexport default DropdownContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC9D,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}