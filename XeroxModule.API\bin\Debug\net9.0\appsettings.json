{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=XeroxModuleDB;Trusted_Connection=true;MultipleActiveResultSets=true"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyForJWTTokenGenerationThatShouldBeAtLeast32Characters", "Issuer": "XeroxModuleAPI", "Audience": "XeroxModuleClient", "ExpirationHours": 24}, "FileUploadSettings": {"MaxFileSize": 52428800, "AllowedFileTypes": [".pdf", ".doc", ".docx", ".zip", ".rar", ".jpg", ".jpeg", ".png"], "UploadPath": "uploads"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}