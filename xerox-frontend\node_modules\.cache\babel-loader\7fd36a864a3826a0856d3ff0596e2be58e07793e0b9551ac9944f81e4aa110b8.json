{"ast": null, "code": "import { useState } from 'react';\nimport useEffect from './useIsomorphicEffect';\nconst targetMap = new WeakMap();\nlet resizeObserver;\nfunction getResizeObserver() {\n  // eslint-disable-next-line no-return-assign\n  return resizeObserver = resizeObserver || new window.ResizeObserver(entries => {\n    entries.forEach(entry => {\n      const handler = targetMap.get(entry.target);\n      if (handler) handler(entry.contentRect);\n    });\n  });\n}\n\n/**\n * Efficiently observe size changes on an element. Depends on the `ResizeObserver` api,\n * and polyfills are needed in older browsers.\n *\n * ```ts\n * const [ref, attachRef] = useCallbackRef(null);\n *\n * const rect = useResizeObserver(ref);\n *\n * return (\n *  <div ref={attachRef}>\n *    {JSON.stringify(rect)}\n *  </div>\n * )\n * ```\n *\n * @param element The DOM element to observe\n */\nexport default function useResizeObserver(element) {\n  const [rect, setRect] = useState(null);\n  useEffect(() => {\n    if (!element) return;\n    getResizeObserver().observe(element);\n    setRect(element.getBoundingClientRect());\n    targetMap.set(element, rect => {\n      setRect(rect);\n    });\n    return () => {\n      targetMap.delete(element);\n    };\n  }, [element]);\n  return rect;\n}", "map": {"version": 3, "names": ["useState", "useEffect", "targetMap", "WeakMap", "resizeObserver", "getResizeObserver", "window", "ResizeObserver", "entries", "for<PERSON>ach", "entry", "handler", "get", "target", "contentRect", "useResizeObserver", "element", "rect", "setRect", "observe", "getBoundingClientRect", "set", "delete"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useResizeObserver.js"], "sourcesContent": ["import { useState } from 'react';\nimport useEffect from './useIsomorphicEffect';\nconst targetMap = new WeakMap();\nlet resizeObserver;\nfunction getResizeObserver() {\n  // eslint-disable-next-line no-return-assign\n  return resizeObserver = resizeObserver || new window.ResizeObserver(entries => {\n    entries.forEach(entry => {\n      const handler = targetMap.get(entry.target);\n      if (handler) handler(entry.contentRect);\n    });\n  });\n}\n\n/**\n * Efficiently observe size changes on an element. Depends on the `ResizeObserver` api,\n * and polyfills are needed in older browsers.\n *\n * ```ts\n * const [ref, attachRef] = useCallbackRef(null);\n *\n * const rect = useResizeObserver(ref);\n *\n * return (\n *  <div ref={attachRef}>\n *    {JSON.stringify(rect)}\n *  </div>\n * )\n * ```\n *\n * @param element The DOM element to observe\n */\nexport default function useResizeObserver(element) {\n  const [rect, setRect] = useState(null);\n  useEffect(() => {\n    if (!element) return;\n    getResizeObserver().observe(element);\n    setRect(element.getBoundingClientRect());\n    targetMap.set(element, rect => {\n      setRect(rect);\n    });\n    return () => {\n      targetMap.delete(element);\n    };\n  }, [element]);\n  return rect;\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,MAAMC,SAAS,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC/B,IAAIC,cAAc;AAClB,SAASC,iBAAiBA,CAAA,EAAG;EAC3B;EACA,OAAOD,cAAc,GAAGA,cAAc,IAAI,IAAIE,MAAM,CAACC,cAAc,CAACC,OAAO,IAAI;IAC7EA,OAAO,CAACC,OAAO,CAACC,KAAK,IAAI;MACvB,MAAMC,OAAO,GAAGT,SAAS,CAACU,GAAG,CAACF,KAAK,CAACG,MAAM,CAAC;MAC3C,IAAIF,OAAO,EAAEA,OAAO,CAACD,KAAK,CAACI,WAAW,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAACC,OAAO,EAAE;EACjD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtCC,SAAS,CAAC,MAAM;IACd,IAAI,CAACe,OAAO,EAAE;IACdX,iBAAiB,CAAC,CAAC,CAACc,OAAO,CAACH,OAAO,CAAC;IACpCE,OAAO,CAACF,OAAO,CAACI,qBAAqB,CAAC,CAAC,CAAC;IACxClB,SAAS,CAACmB,GAAG,CAACL,OAAO,EAAEC,IAAI,IAAI;MAC7BC,OAAO,CAACD,IAAI,CAAC;IACf,CAAC,CAAC;IACF,OAAO,MAAM;MACXf,SAAS,CAACoB,MAAM,CAACN,OAAO,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,OAAOC,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}