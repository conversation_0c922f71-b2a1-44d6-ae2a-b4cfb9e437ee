{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport createUtilityClassName, { responsivePropType } from './createUtilityClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Stack = /*#__PURE__*/React.forwardRef(({\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  direction,\n  gap,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, direction === 'horizontal' ? 'hstack' : 'vstack');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    className: classNames(className, bsPrefix, ...createUtilityClassName({\n      gap\n    }, breakpoints, minBreakpoint))\n  });\n});\nStack.displayName = 'Stack';\nexport default Stack;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "useBootstrapBreakpoints", "useBootstrapMinBreakpoint", "createUtilityClassName", "responsivePropType", "jsx", "_jsx", "<PERSON><PERSON>", "forwardRef", "as", "Component", "bsPrefix", "className", "direction", "gap", "props", "ref", "breakpoints", "minBreakpoint", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/Stack.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useBootstrapBreakpoints, useBootstrapMinBreakpoint } from './ThemeProvider';\nimport createUtilityClassName, { responsivePropType } from './createUtilityClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Stack = /*#__PURE__*/React.forwardRef(({\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  direction,\n  gap,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, direction === 'horizontal' ? 'hstack' : 'vstack');\n  const breakpoints = useBootstrapBreakpoints();\n  const minBreakpoint = useBootstrapMinBreakpoint();\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    className: classNames(className, bsPrefix, ...createUtilityClassName({\n      gap\n    }, breakpoints, minBreakpoint))\n  });\n});\nStack.displayName = 'Stack';\nexport default Stack;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,EAAEC,uBAAuB,EAAEC,yBAAyB,QAAQ,iBAAiB;AACxG,OAAOC,sBAAsB,IAAIC,kBAAkB,QAAQ,wBAAwB;AACnF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,KAAK,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAC;EAC3CC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrBC,QAAQ;EACRC,SAAS;EACTC,SAAS;EACTC,GAAG;EACH,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTL,QAAQ,GAAGX,kBAAkB,CAACW,QAAQ,EAAEE,SAAS,KAAK,YAAY,GAAG,QAAQ,GAAG,QAAQ,CAAC;EACzF,MAAMI,WAAW,GAAGhB,uBAAuB,CAAC,CAAC;EAC7C,MAAMiB,aAAa,GAAGhB,yBAAyB,CAAC,CAAC;EACjD,OAAO,aAAaI,IAAI,CAACI,SAAS,EAAE;IAClC,GAAGK,KAAK;IACRC,GAAG,EAAEA,GAAG;IACRJ,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAED,QAAQ,EAAE,GAAGR,sBAAsB,CAAC;MACnEW;IACF,CAAC,EAAEG,WAAW,EAAEC,aAAa,CAAC;EAChC,CAAC,CAAC;AACJ,CAAC,CAAC;AACFX,KAAK,CAACY,WAAW,GAAG,OAAO;AAC3B,eAAeZ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}