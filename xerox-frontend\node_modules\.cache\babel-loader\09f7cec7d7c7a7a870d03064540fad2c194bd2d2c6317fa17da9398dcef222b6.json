{"ast": null, "code": "import { useMemo } from 'react';\nconst toFnRef = ref => !ref || typeof ref === 'function' ? ref : value => {\n  ref.current = value;\n};\nexport function mergeRefs(refA, refB) {\n  const a = toFnRef(refA);\n  const b = toFnRef(refB);\n  return value => {\n    if (a) a(value);\n    if (b) b(value);\n  };\n}\n\n/**\n * Create and returns a single callback ref composed from two other Refs.\n *\n * ```tsx\n * const Button = React.forwardRef((props, ref) => {\n *   const [element, attachRef] = useCallbackRef<HTMLButtonElement>();\n *   const mergedRef = useMergedRefs(ref, attachRef);\n *\n *   return <button ref={mergedRef} {...props}/>\n * })\n * ```\n *\n * @param refA A Callback or mutable Ref\n * @param refB A Callback or mutable Ref\n * @category refs\n */\nfunction useMergedRefs(refA, refB) {\n  return useMemo(() => mergeRefs(refA, refB), [refA, refB]);\n}\nexport default useMergedRefs;", "map": {"version": 3, "names": ["useMemo", "toFnRef", "ref", "value", "current", "mergeRefs", "refA", "refB", "a", "b", "useMergedRefs"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@restart/hooks/esm/useMergedRefs.js"], "sourcesContent": ["import { useMemo } from 'react';\nconst toFnRef = ref => !ref || typeof ref === 'function' ? ref : value => {\n  ref.current = value;\n};\nexport function mergeRefs(refA, refB) {\n  const a = toFnRef(refA);\n  const b = toFnRef(refB);\n  return value => {\n    if (a) a(value);\n    if (b) b(value);\n  };\n}\n\n/**\n * Create and returns a single callback ref composed from two other Refs.\n *\n * ```tsx\n * const Button = React.forwardRef((props, ref) => {\n *   const [element, attachRef] = useCallbackRef<HTMLButtonElement>();\n *   const mergedRef = useMergedRefs(ref, attachRef);\n *\n *   return <button ref={mergedRef} {...props}/>\n * })\n * ```\n *\n * @param refA A Callback or mutable Ref\n * @param refB A Callback or mutable Ref\n * @category refs\n */\nfunction useMergedRefs(refA, refB) {\n  return useMemo(() => mergeRefs(refA, refB), [refA, refB]);\n}\nexport default useMergedRefs;"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,MAAMC,OAAO,GAAGC,GAAG,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,UAAU,GAAGA,GAAG,GAAGC,KAAK,IAAI;EACxED,GAAG,CAACE,OAAO,GAAGD,KAAK;AACrB,CAAC;AACD,OAAO,SAASE,SAASA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACpC,MAAMC,CAAC,GAAGP,OAAO,CAACK,IAAI,CAAC;EACvB,MAAMG,CAAC,GAAGR,OAAO,CAACM,IAAI,CAAC;EACvB,OAAOJ,KAAK,IAAI;IACd,IAAIK,CAAC,EAAEA,CAAC,CAACL,KAAK,CAAC;IACf,IAAIM,CAAC,EAAEA,CAAC,CAACN,KAAK,CAAC;EACjB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,aAAaA,CAACJ,IAAI,EAAEC,IAAI,EAAE;EACjC,OAAOP,OAAO,CAAC,MAAMK,SAAS,CAACC,IAAI,EAAEC,IAAI,CAAC,EAAE,CAACD,IAAI,EAAEC,IAAI,CAAC,CAAC;AAC3D;AACA,eAAeG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}