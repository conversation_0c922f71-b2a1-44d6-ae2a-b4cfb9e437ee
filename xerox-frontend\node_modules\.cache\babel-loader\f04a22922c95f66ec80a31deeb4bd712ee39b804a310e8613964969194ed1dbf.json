{"ast": null, "code": "const _excluded = [\"onEnter\", \"onEntering\", \"onEntered\", \"onExit\", \"onExiting\", \"onExited\", \"addEndListener\", \"children\"];\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.indexOf(n) >= 0) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nimport { cloneElement, useCallback, useRef } from 'react';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport { getChildRef } from './utils';\n/**\n * Normalizes RTG transition callbacks with nodeRef to better support\n * strict mode.\n *\n * @param props Transition props.\n * @returns Normalized transition props.\n */\nexport default function useRTGTransitionProps(_ref) {\n  let {\n      onEnter,\n      onEntering,\n      onEntered,\n      onExit,\n      onExiting,\n      onExited,\n      addEndListener,\n      children\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const nodeRef = useRef(null);\n  const mergedRef = useMergedRefs(nodeRef, getChildRef(children));\n  const normalize = callback => param => {\n    if (callback && nodeRef.current) {\n      callback(nodeRef.current, param);\n    }\n  };\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  const handleEnter = useCallback(normalize(onEnter), [onEnter]);\n  const handleEntering = useCallback(normalize(onEntering), [onEntering]);\n  const handleEntered = useCallback(normalize(onEntered), [onEntered]);\n  const handleExit = useCallback(normalize(onExit), [onExit]);\n  const handleExiting = useCallback(normalize(onExiting), [onExiting]);\n  const handleExited = useCallback(normalize(onExited), [onExited]);\n  const handleAddEndListener = useCallback(normalize(addEndListener), [addEndListener]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n\n  return Object.assign({}, props, {\n    nodeRef\n  }, onEnter && {\n    onEnter: handleEnter\n  }, onEntering && {\n    onEntering: handleEntering\n  }, onEntered && {\n    onEntered: handleEntered\n  }, onExit && {\n    onExit: handleExit\n  }, onExiting && {\n    onExiting: handleExiting\n  }, onExited && {\n    onExited: handleExited\n  }, addEndListener && {\n    addEndListener: handleAddEndListener\n  }, {\n    children: typeof children === 'function' ? (status, innerProps) =>\n    // TODO: Types for RTG missing innerProps, so need to cast.\n    children(status, Object.assign({}, innerProps, {\n      ref: mergedRef\n    })) : /*#__PURE__*/cloneElement(children, {\n      ref: mergedRef\n    })\n  });\n}", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "hasOwnProperty", "call", "indexOf", "cloneElement", "useCallback", "useRef", "useMergedRefs", "getChildRef", "useRTGTransitionProps", "_ref", "onEnter", "onEntering", "onEntered", "onExit", "onExiting", "onExited", "addEndListener", "children", "props", "nodeRef", "mergedRef", "normalize", "callback", "param", "current", "handleEnter", "handleEntering", "handleEntered", "handleExit", "handleExiting", "handleExited", "handleAddEndListener", "Object", "assign", "status", "innerProps", "ref"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@restart/ui/esm/useRTGTransitionProps.js"], "sourcesContent": ["const _excluded = [\"onEnter\", \"onEntering\", \"onEntered\", \"onExit\", \"onExiting\", \"onExited\", \"addEndListener\", \"children\"];\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\nimport { cloneElement, useCallback, useRef } from 'react';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport { getChildRef } from './utils';\n/**\n * Normalizes RTG transition callbacks with nodeRef to better support\n * strict mode.\n *\n * @param props Transition props.\n * @returns Normalized transition props.\n */\nexport default function useRTGTransitionProps(_ref) {\n  let {\n      onEnter,\n      onEntering,\n      onEntered,\n      onExit,\n      onExiting,\n      onExited,\n      addEndListener,\n      children\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const nodeRef = useRef(null);\n  const mergedRef = useMergedRefs(nodeRef, getChildRef(children));\n  const normalize = callback => param => {\n    if (callback && nodeRef.current) {\n      callback(nodeRef.current, param);\n    }\n  };\n\n  /* eslint-disable react-hooks/exhaustive-deps */\n  const handleEnter = useCallback(normalize(onEnter), [onEnter]);\n  const handleEntering = useCallback(normalize(onEntering), [onEntering]);\n  const handleEntered = useCallback(normalize(onEntered), [onEntered]);\n  const handleExit = useCallback(normalize(onExit), [onExit]);\n  const handleExiting = useCallback(normalize(onExiting), [onExiting]);\n  const handleExited = useCallback(normalize(onExited), [onExited]);\n  const handleAddEndListener = useCallback(normalize(addEndListener), [addEndListener]);\n  /* eslint-enable react-hooks/exhaustive-deps */\n\n  return Object.assign({}, props, {\n    nodeRef\n  }, onEnter && {\n    onEnter: handleEnter\n  }, onEntering && {\n    onEntering: handleEntering\n  }, onEntered && {\n    onEntered: handleEntered\n  }, onExit && {\n    onExit: handleExit\n  }, onExiting && {\n    onExiting: handleExiting\n  }, onExited && {\n    onExited: handleExited\n  }, addEndListener && {\n    addEndListener: handleAddEndListener\n  }, {\n    children: typeof children === 'function' ? (status, innerProps) =>\n    // TODO: Types for RTG missing innerProps, so need to cast.\n    children(status, Object.assign({}, innerProps, {\n      ref: mergedRef\n    })) : /*#__PURE__*/cloneElement(children, {\n      ref: mergedRef\n    })\n  });\n}"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,gBAAgB,EAAE,UAAU,CAAC;AACzH,SAASC,6BAA6BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAI,CAAC,CAAC,CAACI,cAAc,CAACC,IAAI,CAACL,CAAC,EAAEG,CAAC,CAAC,EAAE;IAAE,IAAIF,CAAC,CAACK,OAAO,CAACH,CAAC,CAAC,IAAI,CAAC,EAAE;IAAUD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACpM,SAASK,YAAY,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACzD,OAAOC,aAAa,MAAM,8BAA8B;AACxD,SAASC,WAAW,QAAQ,SAAS;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAClD,IAAI;MACAC,OAAO;MACPC,UAAU;MACVC,SAAS;MACTC,MAAM;MACNC,SAAS;MACTC,QAAQ;MACRC,cAAc;MACdC;IACF,CAAC,GAAGR,IAAI;IACRS,KAAK,GAAGvB,6BAA6B,CAACc,IAAI,EAAEf,SAAS,CAAC;EACxD,MAAMyB,OAAO,GAAGd,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMe,SAAS,GAAGd,aAAa,CAACa,OAAO,EAAEZ,WAAW,CAACU,QAAQ,CAAC,CAAC;EAC/D,MAAMI,SAAS,GAAGC,QAAQ,IAAIC,KAAK,IAAI;IACrC,IAAID,QAAQ,IAAIH,OAAO,CAACK,OAAO,EAAE;MAC/BF,QAAQ,CAACH,OAAO,CAACK,OAAO,EAAED,KAAK,CAAC;IAClC;EACF,CAAC;;EAED;EACA,MAAME,WAAW,GAAGrB,WAAW,CAACiB,SAAS,CAACX,OAAO,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EAC9D,MAAMgB,cAAc,GAAGtB,WAAW,CAACiB,SAAS,CAACV,UAAU,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EACvE,MAAMgB,aAAa,GAAGvB,WAAW,CAACiB,SAAS,CAACT,SAAS,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACpE,MAAMgB,UAAU,GAAGxB,WAAW,CAACiB,SAAS,CAACR,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAC3D,MAAMgB,aAAa,GAAGzB,WAAW,CAACiB,SAAS,CAACP,SAAS,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACpE,MAAMgB,YAAY,GAAG1B,WAAW,CAACiB,SAAS,CAACN,QAAQ,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACjE,MAAMgB,oBAAoB,GAAG3B,WAAW,CAACiB,SAAS,CAACL,cAAc,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EACrF;;EAEA,OAAOgB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEf,KAAK,EAAE;IAC9BC;EACF,CAAC,EAAET,OAAO,IAAI;IACZA,OAAO,EAAEe;EACX,CAAC,EAAEd,UAAU,IAAI;IACfA,UAAU,EAAEe;EACd,CAAC,EAAEd,SAAS,IAAI;IACdA,SAAS,EAAEe;EACb,CAAC,EAAEd,MAAM,IAAI;IACXA,MAAM,EAAEe;EACV,CAAC,EAAEd,SAAS,IAAI;IACdA,SAAS,EAAEe;EACb,CAAC,EAAEd,QAAQ,IAAI;IACbA,QAAQ,EAAEe;EACZ,CAAC,EAAEd,cAAc,IAAI;IACnBA,cAAc,EAAEe;EAClB,CAAC,EAAE;IACDd,QAAQ,EAAE,OAAOA,QAAQ,KAAK,UAAU,GAAG,CAACiB,MAAM,EAAEC,UAAU;IAC9D;IACAlB,QAAQ,CAACiB,MAAM,EAAEF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEE,UAAU,EAAE;MAC7CC,GAAG,EAAEhB;IACP,CAAC,CAAC,CAAC,GAAG,aAAajB,YAAY,CAACc,QAAQ,EAAE;MACxCmB,GAAG,EAAEhB;IACP,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}