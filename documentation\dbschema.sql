
CREATE TABLE ZRX_XeroxCenter (
	XeroxCenterID             INT               PRIMARY KEY,
	XeroxCenterName           VARCHAR(50)       NOT NULL,
	Location                  VARCHAR(100)      NOT NULL,
	Contact<PERSON>erson             VARCHAR(50)       NULL,
	Email                     VARCHAR(50)       NULL,
	PhoneNumber               VARCHAR(20)       NOT NULL,
	TotalJobs                 INT               NULL,
	PendingJobs               INT               NOT NULL,
	IsActive                  BIT               NOT NULL,
	CreatedUserID             INT               NOT NULL,
	ModifiedUserID            INT               NULL,
	Created                   DATETIME          NOT NULL,
	Modified                  DATETIME          NULL,
	Description               VARCHAR(255)      NULL
    
    CONSTRAINT FK_ZRX_XeroxCenter_ZRX_User_CreatedUserID FOREIGN KEY (CreatedUserID) REFERENCES ZRX_User(UserID),
	CONSTRAINT FK_ZRX_XeroxCenter_ZRX_User_ModifiedUserID FOREIGN KEY (ModifiedUserID) REFERENCES ZRX_User(UserID)
);
				


CREATE TABLE ZRX_PrintJob (
	PrintJobID                INT               PRIMARY KEY,
	XeroxCenterID             INT               NOT NULL,
	<PERSON>UploadID              INT               NOT NULL,
	Cost                      INT               NULL,
	Status                    VARCHAR(50)       NOT NULL,
	EstimatedCompletionTime   DATETIME          NULL,
	ActualCompletionTime      DATETIME          NULL,
	DeliveredAt               DATETIME          NULL,
	CreatedUserID             INT               NOT NULL,
	ModifiedUserID            INT               NULL,
	Created                   DATETIME          NOT NULL,
	Modified                  DATETIME          NULL,
	Description               VARCHAR(255)      NULL
    
    CONSTRAINT FK_ZRX_PrintJob_ZRX_XeroxCenter_XeroxCenterID FOREIGN KEY (XeroxCenterID) REFERENCES ZRX_XeroxCenter(XeroxCenterID),
	CONSTRAINT FK_ZRX_PrintJob_ZRX_File_FileUploadID FOREIGN KEY (FileUploadID) REFERENCES ZRX_FileUpload(FileUploadID),
	CONSTRAINT FK_ZRX_PrintJob_ZRX_User_CreatedUserID FOREIGN KEY (CreatedUserID) REFERENCES ZRX_User(UserID),
	CONSTRAINT FK_ZRX_PrintJob_ZRX_User_ModifiedUserID FOREIGN KEY (ModifiedUserID) REFERENCES ZRX_User(UserID)
);
				


CREATE TABLE ZRX_FileUpload (
	FileUploadID                INT               PRIMARY KEY,
	FileName                    VARCHAR(50)       NOT NULL,
	FilePath                    VARCHAR(50)       NOT NULL,
	Remarks                     VARCHAR(100)      NULL,
	StudentID                   INT               NOT NULL,
	PreferredXeroxCenterID      INT               NULL,
	UploadedAt                  DATETIME          NOT NULL,
	PrintType                   VARCHAR(50)       NOT NULL,
	CreatedUserID               INT               NOT NULL,
	ModifiedUserID              INT               NULL,
	Created                     DATETIME          NOT NULL,
	Modified                    DATETIME          NULL,
	Description                 VARCHAR(255)      NULL
    
    CONSTRAINT FK_ZRX_FileUpload_ZRX_Student_StudentID FOREIGN KEY (StudentID) REFERENCES STU_Student(StudentID),
	CONSTRAINT FK_ZRX_FileUpload_ZRX_XeroxCenter_PreferredXeroxCenterID FOREIGN KEY (PreferredXeroxCenterID) REFERENCES ZRX_XeroxCenter(XeroxCenterID),
	CONSTRAINT FK_ZRX_FileUpload_ZRX_User_CreatedUserID FOREIGN KEY (CreatedUserID) REFERENCES ZRX_User(UserID),
	CONSTRAINT FK_ZRX_FileUpload_ZRX_User_ModifiedUserID FOREIGN KEY (ModifiedUserID) REFERENCES ZRX_User(UserID)
);
