{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.API\\XeroxModule.API.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.API\\XeroxModule.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.API\\XeroxModule.API.csproj", "projectName": "XeroxModule.API", "projectPath": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.API\\XeroxModule.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Application\\XeroxModule.Application.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Application\\XeroxModule.Application.csproj"}, "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Infrastructure\\XeroxModule.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Infrastructure\\XeroxModule.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Application\\XeroxModule.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Application\\XeroxModule.Application.csproj", "projectName": "XeroxModule.Application", "projectPath": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Application\\XeroxModule.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Core\\XeroxModule.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Core\\XeroxModule.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[9.0.6, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.12.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Core\\XeroxModule.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Core\\XeroxModule.Core.csproj", "projectName": "XeroxModule.Core", "projectPath": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Core\\XeroxModule.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Infrastructure\\XeroxModule.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Infrastructure\\XeroxModule.Infrastructure.csproj", "projectName": "XeroxModule.Infrastructure", "projectPath": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Infrastructure\\XeroxModule.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Core\\XeroxModule.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Xerox Module\\XeroxModule.Core\\XeroxModule.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.101/PortableRuntimeIdentifierGraph.json"}}}}}