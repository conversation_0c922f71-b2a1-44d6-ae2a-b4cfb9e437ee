[{"C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Navbar.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Login.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Register.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\XeroxCenterDashboard.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\StudentDashboard.tsx": "9"}, {"size": 771, "mtime": 1749882534472, "results": "10", "hashOfConfig": "11"}, {"size": 425, "mtime": 1749882098393, "results": "12", "hashOfConfig": "11"}, {"size": 2049, "mtime": 1749882321465, "results": "13", "hashOfConfig": "11"}, {"size": 4855, "mtime": 1749882926549, "results": "14", "hashOfConfig": "11"}, {"size": 3438, "mtime": 1749882354078, "results": "15", "hashOfConfig": "11"}, {"size": 3870, "mtime": 1749882371894, "results": "16", "hashOfConfig": "11"}, {"size": 15160, "mtime": 1749882413933, "results": "17", "hashOfConfig": "11"}, {"size": 16509, "mtime": 1749882523248, "results": "18", "hashOfConfig": "11"}, {"size": 14617, "mtime": 1749882469035, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gad31y", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\AuthContext.tsx", ["47"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Login.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Register.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\XeroxCenterDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\StudentDashboard.tsx", [], [], {"ruleId": "48", "severity": 1, "message": "49", "line": 75, "column": 6, "nodeType": "50", "endLine": 75, "endColumn": 13, "suggestions": "51"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCurrentUser'. Either include it or remove the dependency array.", "ArrayExpression", ["52"], {"desc": "53", "fix": "54"}, "Update the dependencies array to be: [fetchCurrentUser, token]", {"range": "55", "text": "56"}, [1998, 2005], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON>, token]"]