{"ast": null, "code": "import invariant from 'invariant';\nvar noop = function noop() {};\nfunction readOnlyPropType(handler, name) {\n  return function (props, propName) {\n    if (props[propName] !== undefined) {\n      if (!props[handler]) {\n        return new Error(\"You have provided a `\" + propName + \"` prop to `\" + name + \"` \" + (\"without an `\" + handler + \"` handler prop. This will render a read-only field. \") + (\"If the field should be mutable use `\" + defaultKey(propName) + \"`. \") + (\"Otherwise, set `\" + handler + \"`.\"));\n      }\n    }\n  };\n}\nexport function uncontrolledPropTypes(controlledValues, displayName) {\n  var propTypes = {};\n  Object.keys(controlledValues).forEach(function (prop) {\n    // add default propTypes for folks that use runtime checks\n    propTypes[defaultKey(prop)] = noop;\n    if (process.env.NODE_ENV !== 'production') {\n      var handler = controlledValues[prop];\n      !(typeof handler === 'string' && handler.trim().length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Uncontrollable - [%s]: the prop `%s` needs a valid handler key name in order to make it uncontrollable', displayName, prop) : invariant(false) : void 0;\n      propTypes[prop] = readOnlyPropType(handler, displayName);\n    }\n  });\n  return propTypes;\n}\nexport function isProp(props, prop) {\n  return props[prop] !== undefined;\n}\nexport function defaultKey(key) {\n  return 'default' + key.charAt(0).toUpperCase() + key.substr(1);\n}\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\nexport function canAcceptRef(component) {\n  return !!component && (typeof component !== 'function' || component.prototype && component.prototype.isReactComponent);\n}", "map": {"version": 3, "names": ["invariant", "noop", "readOnlyPropType", "handler", "name", "props", "propName", "undefined", "Error", "defaultKey", "uncontrolledPropTypes", "controlledValues", "displayName", "propTypes", "Object", "keys", "for<PERSON>ach", "prop", "process", "env", "NODE_ENV", "trim", "length", "isProp", "key", "char<PERSON>t", "toUpperCase", "substr", "canAcceptRef", "component", "prototype", "isReactComponent"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/uncontrollable/lib/esm/utils.js"], "sourcesContent": ["import invariant from 'invariant';\n\nvar noop = function noop() {};\n\nfunction readOnlyPropType(handler, name) {\n  return function (props, propName) {\n    if (props[propName] !== undefined) {\n      if (!props[handler]) {\n        return new Error(\"You have provided a `\" + propName + \"` prop to `\" + name + \"` \" + (\"without an `\" + handler + \"` handler prop. This will render a read-only field. \") + (\"If the field should be mutable use `\" + defaultKey(propName) + \"`. \") + (\"Otherwise, set `\" + handler + \"`.\"));\n      }\n    }\n  };\n}\n\nexport function uncontrolledPropTypes(controlledValues, displayName) {\n  var propTypes = {};\n  Object.keys(controlledValues).forEach(function (prop) {\n    // add default propTypes for folks that use runtime checks\n    propTypes[defaultKey(prop)] = noop;\n\n    if (process.env.NODE_ENV !== 'production') {\n      var handler = controlledValues[prop];\n      !(typeof handler === 'string' && handler.trim().length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Uncontrollable - [%s]: the prop `%s` needs a valid handler key name in order to make it uncontrollable', displayName, prop) : invariant(false) : void 0;\n      propTypes[prop] = readOnlyPropType(handler, displayName);\n    }\n  });\n  return propTypes;\n}\nexport function isProp(props, prop) {\n  return props[prop] !== undefined;\n}\nexport function defaultKey(key) {\n  return 'default' + key.charAt(0).toUpperCase() + key.substr(1);\n}\n/**\n * Copyright (c) 2013-present, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n */\n\nexport function canAcceptRef(component) {\n  return !!component && (typeof component !== 'function' || component.prototype && component.prototype.isReactComponent);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,WAAW;AAEjC,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAG,CAAC,CAAC;AAE7B,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACvC,OAAO,UAAUC,KAAK,EAAEC,QAAQ,EAAE;IAChC,IAAID,KAAK,CAACC,QAAQ,CAAC,KAAKC,SAAS,EAAE;MACjC,IAAI,CAACF,KAAK,CAACF,OAAO,CAAC,EAAE;QACnB,OAAO,IAAIK,KAAK,CAAC,uBAAuB,GAAGF,QAAQ,GAAG,aAAa,GAAGF,IAAI,GAAG,IAAI,IAAI,cAAc,GAAGD,OAAO,GAAG,sDAAsD,CAAC,IAAI,sCAAsC,GAAGM,UAAU,CAACH,QAAQ,CAAC,GAAG,KAAK,CAAC,IAAI,kBAAkB,GAAGH,OAAO,GAAG,IAAI,CAAC,CAAC;MAC5R;IACF;EACF,CAAC;AACH;AAEA,OAAO,SAASO,qBAAqBA,CAACC,gBAAgB,EAAEC,WAAW,EAAE;EACnE,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClBC,MAAM,CAACC,IAAI,CAACJ,gBAAgB,CAAC,CAACK,OAAO,CAAC,UAAUC,IAAI,EAAE;IACpD;IACAJ,SAAS,CAACJ,UAAU,CAACQ,IAAI,CAAC,CAAC,GAAGhB,IAAI;IAElC,IAAIiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIjB,OAAO,GAAGQ,gBAAgB,CAACM,IAAI,CAAC;MACpC,EAAE,OAAOd,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACkB,IAAI,CAAC,CAAC,CAACC,MAAM,CAAC,GAAGJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGpB,SAAS,CAAC,KAAK,EAAE,wGAAwG,EAAEY,WAAW,EAAEK,IAAI,CAAC,GAAGjB,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;MAC3Qa,SAAS,CAACI,IAAI,CAAC,GAAGf,gBAAgB,CAACC,OAAO,EAAES,WAAW,CAAC;IAC1D;EACF,CAAC,CAAC;EACF,OAAOC,SAAS;AAClB;AACA,OAAO,SAASU,MAAMA,CAAClB,KAAK,EAAEY,IAAI,EAAE;EAClC,OAAOZ,KAAK,CAACY,IAAI,CAAC,KAAKV,SAAS;AAClC;AACA,OAAO,SAASE,UAAUA,CAACe,GAAG,EAAE;EAC9B,OAAO,SAAS,GAAGA,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,GAAG,CAACG,MAAM,CAAC,CAAC,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,YAAYA,CAACC,SAAS,EAAE;EACtC,OAAO,CAAC,CAACA,SAAS,KAAK,OAAOA,SAAS,KAAK,UAAU,IAAIA,SAAS,CAACC,SAAS,IAAID,SAAS,CAACC,SAAS,CAACC,gBAAgB,CAAC;AACxH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}