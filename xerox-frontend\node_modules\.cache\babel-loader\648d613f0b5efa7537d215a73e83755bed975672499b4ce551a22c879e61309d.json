{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useButtonProps } from '@restart/ui/Button';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Button = /*#__PURE__*/React.forwardRef(({\n  as,\n  bsPrefix,\n  variant = 'primary',\n  size,\n  active = false,\n  disabled = false,\n  className,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn');\n  const [buttonProps, {\n    tagName\n  }] = useButtonProps({\n    tagName: as,\n    disabled,\n    ...props\n  });\n  const Component = tagName;\n  return /*#__PURE__*/_jsx(Component, {\n    ...buttonProps,\n    ...props,\n    ref: ref,\n    disabled: disabled,\n    className: classNames(className, prefix, active && 'active', variant && `${prefix}-${variant}`, size && `${prefix}-${size}`, props.href && disabled && 'disabled')\n  });\n});\nButton.displayName = 'Button';\nexport default Button;", "map": {"version": 3, "names": ["classNames", "React", "useButtonProps", "useBootstrapPrefix", "jsx", "_jsx", "<PERSON><PERSON>", "forwardRef", "as", "bsPrefix", "variant", "size", "active", "disabled", "className", "props", "ref", "prefix", "buttonProps", "tagName", "Component", "href", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/Button.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useButtonProps } from '@restart/ui/Button';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Button = /*#__PURE__*/React.forwardRef(({\n  as,\n  bsPrefix,\n  variant = 'primary',\n  size,\n  active = false,\n  disabled = false,\n  className,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn');\n  const [buttonProps, {\n    tagName\n  }] = useButtonProps({\n    tagName: as,\n    disabled,\n    ...props\n  });\n  const Component = tagName;\n  return /*#__PURE__*/_jsx(Component, {\n    ...buttonProps,\n    ...props,\n    ref: ref,\n    disabled: disabled,\n    className: classNames(className, prefix, active && 'active', variant && `${prefix}-${variant}`, size && `${prefix}-${size}`, props.href && disabled && 'disabled')\n  });\n});\nButton.displayName = 'Button';\nexport default Button;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,MAAM,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EAC5CC,EAAE;EACFC,QAAQ;EACRC,OAAO,GAAG,SAAS;EACnBC,IAAI;EACJC,MAAM,GAAG,KAAK;EACdC,QAAQ,GAAG,KAAK;EAChBC,SAAS;EACT,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGd,kBAAkB,CAACM,QAAQ,EAAE,KAAK,CAAC;EAClD,MAAM,CAACS,WAAW,EAAE;IAClBC;EACF,CAAC,CAAC,GAAGjB,cAAc,CAAC;IAClBiB,OAAO,EAAEX,EAAE;IACXK,QAAQ;IACR,GAAGE;EACL,CAAC,CAAC;EACF,MAAMK,SAAS,GAAGD,OAAO;EACzB,OAAO,aAAad,IAAI,CAACe,SAAS,EAAE;IAClC,GAAGF,WAAW;IACd,GAAGH,KAAK;IACRC,GAAG,EAAEA,GAAG;IACRH,QAAQ,EAAEA,QAAQ;IAClBC,SAAS,EAAEd,UAAU,CAACc,SAAS,EAAEG,MAAM,EAAEL,MAAM,IAAI,QAAQ,EAAEF,OAAO,IAAI,GAAGO,MAAM,IAAIP,OAAO,EAAE,EAAEC,IAAI,IAAI,GAAGM,MAAM,IAAIN,IAAI,EAAE,EAAEI,KAAK,CAACM,IAAI,IAAIR,QAAQ,IAAI,UAAU;EACnK,CAAC,CAAC;AACJ,CAAC,CAAC;AACFP,MAAM,CAACgB,WAAW,GAAG,QAAQ;AAC7B,eAAehB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}