{"ast": null, "code": "import useEventCallback from '@restart/hooks/useEventCallback';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport { cloneElement, useEffect, useRef } from 'react';\nimport { getChildRef } from './utils';\nfunction NoopTransition({\n  children,\n  in: inProp,\n  onExited,\n  mountOnEnter,\n  unmountOnExit\n}) {\n  const ref = useRef(null);\n  const hasEnteredRef = useRef(inProp);\n  const handleExited = useEventCallback(onExited);\n  useEffect(() => {\n    if (inProp) hasEnteredRef.current = true;else {\n      handleExited(ref.current);\n    }\n  }, [inProp, handleExited]);\n  const combinedRef = useMergedRefs(ref, getChildRef(children));\n  const child = /*#__PURE__*/cloneElement(children, {\n    ref: combinedRef\n  });\n  if (inProp) return child;\n  if (unmountOnExit) {\n    return null;\n  }\n  if (!hasEnteredRef.current && mountOnEnter) {\n    return null;\n  }\n  return child;\n}\nexport default NoopTransition;", "map": {"version": 3, "names": ["useEventCallback", "useMergedRefs", "cloneElement", "useEffect", "useRef", "getChildRef", "NoopTransition", "children", "in", "inProp", "onExited", "mountOnEnter", "unmountOnExit", "ref", "hasEnteredRef", "handleExited", "current", "combinedRef", "child"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@restart/ui/esm/NoopTransition.js"], "sourcesContent": ["import useEventCallback from '@restart/hooks/useEventCallback';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport { cloneElement, useEffect, useRef } from 'react';\nimport { getChildRef } from './utils';\nfunction NoopTransition({\n  children,\n  in: inProp,\n  onExited,\n  mountOnEnter,\n  unmountOnExit\n}) {\n  const ref = useRef(null);\n  const hasEnteredRef = useRef(inProp);\n  const handleExited = useEventCallback(onExited);\n  useEffect(() => {\n    if (inProp) hasEnteredRef.current = true;else {\n      handleExited(ref.current);\n    }\n  }, [inProp, handleExited]);\n  const combinedRef = useMergedRefs(ref, getChildRef(children));\n  const child = /*#__PURE__*/cloneElement(children, {\n    ref: combinedRef\n  });\n  if (inProp) return child;\n  if (unmountOnExit) {\n    return null;\n  }\n  if (!hasEnteredRef.current && mountOnEnter) {\n    return null;\n  }\n  return child;\n}\nexport default NoopTransition;"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,aAAa,MAAM,8BAA8B;AACxD,SAASC,YAAY,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACvD,SAASC,WAAW,QAAQ,SAAS;AACrC,SAASC,cAAcA,CAAC;EACtBC,QAAQ;EACRC,EAAE,EAAEC,MAAM;EACVC,QAAQ;EACRC,YAAY;EACZC;AACF,CAAC,EAAE;EACD,MAAMC,GAAG,GAAGT,MAAM,CAAC,IAAI,CAAC;EACxB,MAAMU,aAAa,GAAGV,MAAM,CAACK,MAAM,CAAC;EACpC,MAAMM,YAAY,GAAGf,gBAAgB,CAACU,QAAQ,CAAC;EAC/CP,SAAS,CAAC,MAAM;IACd,IAAIM,MAAM,EAAEK,aAAa,CAACE,OAAO,GAAG,IAAI,CAAC,KAAK;MAC5CD,YAAY,CAACF,GAAG,CAACG,OAAO,CAAC;IAC3B;EACF,CAAC,EAAE,CAACP,MAAM,EAAEM,YAAY,CAAC,CAAC;EAC1B,MAAME,WAAW,GAAGhB,aAAa,CAACY,GAAG,EAAER,WAAW,CAACE,QAAQ,CAAC,CAAC;EAC7D,MAAMW,KAAK,GAAG,aAAahB,YAAY,CAACK,QAAQ,EAAE;IAChDM,GAAG,EAAEI;EACP,CAAC,CAAC;EACF,IAAIR,MAAM,EAAE,OAAOS,KAAK;EACxB,IAAIN,aAAa,EAAE;IACjB,OAAO,IAAI;EACb;EACA,IAAI,CAACE,aAAa,CAACE,OAAO,IAAIL,YAAY,EAAE;IAC1C,OAAO,IAAI;EACb;EACA,OAAOO,KAAK;AACd;AACA,eAAeZ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}