{"ast": null, "code": "var has = Object.prototype.hasOwnProperty;\nfunction find(iter, tar, key) {\n  for (key of iter.keys()) {\n    if (dequal(key, tar)) return key;\n  }\n}\nexport function dequal(foo, bar) {\n  var ctor, len, tmp;\n  if (foo === bar) return true;\n  if (foo && bar && (ctor = foo.constructor) === bar.constructor) {\n    if (ctor === Date) return foo.getTime() === bar.getTime();\n    if (ctor === RegExp) return foo.toString() === bar.toString();\n    if (ctor === Array) {\n      if ((len = foo.length) === bar.length) {\n        while (len-- && dequal(foo[len], bar[len]));\n      }\n      return len === -1;\n    }\n    if (ctor === Set) {\n      if (foo.size !== bar.size) {\n        return false;\n      }\n      for (len of foo) {\n        tmp = len;\n        if (tmp && typeof tmp === 'object') {\n          tmp = find(bar, tmp);\n          if (!tmp) return false;\n        }\n        if (!bar.has(tmp)) return false;\n      }\n      return true;\n    }\n    if (ctor === Map) {\n      if (foo.size !== bar.size) {\n        return false;\n      }\n      for (len of foo) {\n        tmp = len[0];\n        if (tmp && typeof tmp === 'object') {\n          tmp = find(bar, tmp);\n          if (!tmp) return false;\n        }\n        if (!dequal(len[1], bar.get(tmp))) {\n          return false;\n        }\n      }\n      return true;\n    }\n    if (ctor === ArrayBuffer) {\n      foo = new Uint8Array(foo);\n      bar = new Uint8Array(bar);\n    } else if (ctor === DataView) {\n      if ((len = foo.byteLength) === bar.byteLength) {\n        while (len-- && foo.getInt8(len) === bar.getInt8(len));\n      }\n      return len === -1;\n    }\n    if (ArrayBuffer.isView(foo)) {\n      if ((len = foo.byteLength) === bar.byteLength) {\n        while (len-- && foo[len] === bar[len]);\n      }\n      return len === -1;\n    }\n    if (!ctor || typeof foo === 'object') {\n      len = 0;\n      for (ctor in foo) {\n        if (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n        if (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n      }\n      return Object.keys(bar).length === len;\n    }\n  }\n  return foo !== foo && bar !== bar;\n}", "map": {"version": 3, "names": ["has", "Object", "prototype", "hasOwnProperty", "find", "iter", "tar", "key", "keys", "dequal", "foo", "bar", "ctor", "len", "tmp", "constructor", "Date", "getTime", "RegExp", "toString", "Array", "length", "Set", "size", "Map", "get", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "DataView", "byteLength", "getInt8", "<PERSON><PERSON><PERSON><PERSON>", "call"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/dequal/dist/index.mjs"], "sourcesContent": ["var has = Object.prototype.hasOwnProperty;\n\nfunction find(iter, tar, key) {\n\tfor (key of iter.keys()) {\n\t\tif (dequal(key, tar)) return key;\n\t}\n}\n\nexport function dequal(foo, bar) {\n\tvar ctor, len, tmp;\n\tif (foo === bar) return true;\n\n\tif (foo && bar && (ctor=foo.constructor) === bar.constructor) {\n\t\tif (ctor === Date) return foo.getTime() === bar.getTime();\n\t\tif (ctor === RegExp) return foo.toString() === bar.toString();\n\n\t\tif (ctor === Array) {\n\t\t\tif ((len=foo.length) === bar.length) {\n\t\t\t\twhile (len-- && dequal(foo[len], bar[len]));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ctor === Set) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len;\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!bar.has(tmp)) return false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === Map) {\n\t\t\tif (foo.size !== bar.size) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\tfor (len of foo) {\n\t\t\t\ttmp = len[0];\n\t\t\t\tif (tmp && typeof tmp === 'object') {\n\t\t\t\t\ttmp = find(bar, tmp);\n\t\t\t\t\tif (!tmp) return false;\n\t\t\t\t}\n\t\t\t\tif (!dequal(len[1], bar.get(tmp))) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\n\t\tif (ctor === ArrayBuffer) {\n\t\t\tfoo = new Uint8Array(foo);\n\t\t\tbar = new Uint8Array(bar);\n\t\t} else if (ctor === DataView) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo.getInt8(len) === bar.getInt8(len));\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (ArrayBuffer.isView(foo)) {\n\t\t\tif ((len=foo.byteLength) === bar.byteLength) {\n\t\t\t\twhile (len-- && foo[len] === bar[len]);\n\t\t\t}\n\t\t\treturn len === -1;\n\t\t}\n\n\t\tif (!ctor || typeof foo === 'object') {\n\t\t\tlen = 0;\n\t\t\tfor (ctor in foo) {\n\t\t\t\tif (has.call(foo, ctor) && ++len && !has.call(bar, ctor)) return false;\n\t\t\t\tif (!(ctor in bar) || !dequal(foo[ctor], bar[ctor])) return false;\n\t\t\t}\n\t\t\treturn Object.keys(bar).length === len;\n\t\t}\n\t}\n\n\treturn foo !== foo && bar !== bar;\n}\n"], "mappings": "AAAA,IAAIA,GAAG,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc;AAEzC,SAASC,IAAIA,CAACC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC7B,KAAKA,GAAG,IAAIF,IAAI,CAACG,IAAI,CAAC,CAAC,EAAE;IACxB,IAAIC,MAAM,CAACF,GAAG,EAAED,GAAG,CAAC,EAAE,OAAOC,GAAG;EACjC;AACD;AAEA,OAAO,SAASE,MAAMA,CAACC,GAAG,EAAEC,GAAG,EAAE;EAChC,IAAIC,IAAI,EAAEC,GAAG,EAAEC,GAAG;EAClB,IAAIJ,GAAG,KAAKC,GAAG,EAAE,OAAO,IAAI;EAE5B,IAAID,GAAG,IAAIC,GAAG,IAAI,CAACC,IAAI,GAACF,GAAG,CAACK,WAAW,MAAMJ,GAAG,CAACI,WAAW,EAAE;IAC7D,IAAIH,IAAI,KAAKI,IAAI,EAAE,OAAON,GAAG,CAACO,OAAO,CAAC,CAAC,KAAKN,GAAG,CAACM,OAAO,CAAC,CAAC;IACzD,IAAIL,IAAI,KAAKM,MAAM,EAAE,OAAOR,GAAG,CAACS,QAAQ,CAAC,CAAC,KAAKR,GAAG,CAACQ,QAAQ,CAAC,CAAC;IAE7D,IAAIP,IAAI,KAAKQ,KAAK,EAAE;MACnB,IAAI,CAACP,GAAG,GAACH,GAAG,CAACW,MAAM,MAAMV,GAAG,CAACU,MAAM,EAAE;QACpC,OAAOR,GAAG,EAAE,IAAIJ,MAAM,CAACC,GAAG,CAACG,GAAG,CAAC,EAAEF,GAAG,CAACE,GAAG,CAAC,CAAC,CAAC;MAC5C;MACA,OAAOA,GAAG,KAAK,CAAC,CAAC;IAClB;IAEA,IAAID,IAAI,KAAKU,GAAG,EAAE;MACjB,IAAIZ,GAAG,CAACa,IAAI,KAAKZ,GAAG,CAACY,IAAI,EAAE;QAC1B,OAAO,KAAK;MACb;MACA,KAAKV,GAAG,IAAIH,GAAG,EAAE;QAChBI,GAAG,GAAGD,GAAG;QACT,IAAIC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UACnCA,GAAG,GAAGV,IAAI,CAACO,GAAG,EAAEG,GAAG,CAAC;UACpB,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;QACvB;QACA,IAAI,CAACH,GAAG,CAACX,GAAG,CAACc,GAAG,CAAC,EAAE,OAAO,KAAK;MAChC;MACA,OAAO,IAAI;IACZ;IAEA,IAAIF,IAAI,KAAKY,GAAG,EAAE;MACjB,IAAId,GAAG,CAACa,IAAI,KAAKZ,GAAG,CAACY,IAAI,EAAE;QAC1B,OAAO,KAAK;MACb;MACA,KAAKV,GAAG,IAAIH,GAAG,EAAE;QAChBI,GAAG,GAAGD,GAAG,CAAC,CAAC,CAAC;QACZ,IAAIC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UACnCA,GAAG,GAAGV,IAAI,CAACO,GAAG,EAAEG,GAAG,CAAC;UACpB,IAAI,CAACA,GAAG,EAAE,OAAO,KAAK;QACvB;QACA,IAAI,CAACL,MAAM,CAACI,GAAG,CAAC,CAAC,CAAC,EAAEF,GAAG,CAACc,GAAG,CAACX,GAAG,CAAC,CAAC,EAAE;UAClC,OAAO,KAAK;QACb;MACD;MACA,OAAO,IAAI;IACZ;IAEA,IAAIF,IAAI,KAAKc,WAAW,EAAE;MACzBhB,GAAG,GAAG,IAAIiB,UAAU,CAACjB,GAAG,CAAC;MACzBC,GAAG,GAAG,IAAIgB,UAAU,CAAChB,GAAG,CAAC;IAC1B,CAAC,MAAM,IAAIC,IAAI,KAAKgB,QAAQ,EAAE;MAC7B,IAAI,CAACf,GAAG,GAACH,GAAG,CAACmB,UAAU,MAAMlB,GAAG,CAACkB,UAAU,EAAE;QAC5C,OAAOhB,GAAG,EAAE,IAAIH,GAAG,CAACoB,OAAO,CAACjB,GAAG,CAAC,KAAKF,GAAG,CAACmB,OAAO,CAACjB,GAAG,CAAC,CAAC;MACvD;MACA,OAAOA,GAAG,KAAK,CAAC,CAAC;IAClB;IAEA,IAAIa,WAAW,CAACK,MAAM,CAACrB,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACG,GAAG,GAACH,GAAG,CAACmB,UAAU,MAAMlB,GAAG,CAACkB,UAAU,EAAE;QAC5C,OAAOhB,GAAG,EAAE,IAAIH,GAAG,CAACG,GAAG,CAAC,KAAKF,GAAG,CAACE,GAAG,CAAC,CAAC;MACvC;MACA,OAAOA,GAAG,KAAK,CAAC,CAAC;IAClB;IAEA,IAAI,CAACD,IAAI,IAAI,OAAOF,GAAG,KAAK,QAAQ,EAAE;MACrCG,GAAG,GAAG,CAAC;MACP,KAAKD,IAAI,IAAIF,GAAG,EAAE;QACjB,IAAIV,GAAG,CAACgC,IAAI,CAACtB,GAAG,EAAEE,IAAI,CAAC,IAAI,EAAEC,GAAG,IAAI,CAACb,GAAG,CAACgC,IAAI,CAACrB,GAAG,EAAEC,IAAI,CAAC,EAAE,OAAO,KAAK;QACtE,IAAI,EAAEA,IAAI,IAAID,GAAG,CAAC,IAAI,CAACF,MAAM,CAACC,GAAG,CAACE,IAAI,CAAC,EAAED,GAAG,CAACC,IAAI,CAAC,CAAC,EAAE,OAAO,KAAK;MAClE;MACA,OAAOX,MAAM,CAACO,IAAI,CAACG,GAAG,CAAC,CAACU,MAAM,KAAKR,GAAG;IACvC;EACD;EAEA,OAAOH,GAAG,KAAKA,GAAG,IAAIC,GAAG,KAAKA,GAAG;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}