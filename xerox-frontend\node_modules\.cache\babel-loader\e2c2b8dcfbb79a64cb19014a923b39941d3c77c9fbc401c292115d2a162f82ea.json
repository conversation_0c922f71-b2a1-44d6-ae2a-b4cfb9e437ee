{"ast": null, "code": "import PropTypes from 'prop-types';\nimport { DEFAULT_BREAKPOINTS, DEFAULT_MIN_BREAKPOINT } from './ThemeProvider';\nexport function responsivePropType(propType) {\n  return PropTypes.oneOfType([propType, PropTypes.shape({\n    xs: propType,\n    sm: propType,\n    md: propType,\n    lg: propType,\n    xl: propType,\n    xxl: propType\n  })]);\n}\nexport default function createUtilityClassName(utilityValues, breakpoints = DEFAULT_BREAKPOINTS, minBreakpoint = DEFAULT_MIN_BREAKPOINT) {\n  const classes = [];\n  Object.entries(utilityValues).forEach(([utilName, utilValue]) => {\n    if (utilValue != null) {\n      if (typeof utilValue === 'object') {\n        breakpoints.forEach(brkPoint => {\n          const bpValue = utilValue[brkPoint];\n          if (bpValue != null) {\n            const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n            classes.push(`${utilName}${infix}-${bpValue}`);\n          }\n        });\n      } else {\n        classes.push(`${utilName}-${utilValue}`);\n      }\n    }\n  });\n  return classes;\n}", "map": {"version": 3, "names": ["PropTypes", "DEFAULT_BREAKPOINTS", "DEFAULT_MIN_BREAKPOINT", "responsivePropType", "propType", "oneOfType", "shape", "xs", "sm", "md", "lg", "xl", "xxl", "createUtilityClassName", "utilityValues", "breakpoints", "minBreakpoint", "classes", "Object", "entries", "for<PERSON>ach", "utilName", "utilValue", "brkPoint", "bpValue", "infix", "push"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/createUtilityClasses.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport { DEFAULT_BREAKPOINTS, DEFAULT_MIN_BREAKPOINT } from './ThemeProvider';\nexport function responsivePropType(propType) {\n  return PropTypes.oneOfType([propType, PropTypes.shape({\n    xs: propType,\n    sm: propType,\n    md: propType,\n    lg: propType,\n    xl: propType,\n    xxl: propType\n  })]);\n}\nexport default function createUtilityClassName(utilityValues, breakpoints = DEFAULT_BREAKPOINTS, minBreakpoint = DEFAULT_MIN_BREAKPOINT) {\n  const classes = [];\n  Object.entries(utilityValues).forEach(([utilName, utilValue]) => {\n    if (utilValue != null) {\n      if (typeof utilValue === 'object') {\n        breakpoints.forEach(brkPoint => {\n          const bpValue = utilValue[brkPoint];\n          if (bpValue != null) {\n            const infix = brkPoint !== minBreakpoint ? `-${brkPoint}` : '';\n            classes.push(`${utilName}${infix}-${bpValue}`);\n          }\n        });\n      } else {\n        classes.push(`${utilName}-${utilValue}`);\n      }\n    }\n  });\n  return classes;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,SAASC,mBAAmB,EAAEC,sBAAsB,QAAQ,iBAAiB;AAC7E,OAAO,SAASC,kBAAkBA,CAACC,QAAQ,EAAE;EAC3C,OAAOJ,SAAS,CAACK,SAAS,CAAC,CAACD,QAAQ,EAAEJ,SAAS,CAACM,KAAK,CAAC;IACpDC,EAAE,EAAEH,QAAQ;IACZI,EAAE,EAAEJ,QAAQ;IACZK,EAAE,EAAEL,QAAQ;IACZM,EAAE,EAAEN,QAAQ;IACZO,EAAE,EAAEP,QAAQ;IACZQ,GAAG,EAAER;EACP,CAAC,CAAC,CAAC,CAAC;AACN;AACA,eAAe,SAASS,sBAAsBA,CAACC,aAAa,EAAEC,WAAW,GAAGd,mBAAmB,EAAEe,aAAa,GAAGd,sBAAsB,EAAE;EACvI,MAAMe,OAAO,GAAG,EAAE;EAClBC,MAAM,CAACC,OAAO,CAACL,aAAa,CAAC,CAACM,OAAO,CAAC,CAAC,CAACC,QAAQ,EAAEC,SAAS,CAAC,KAAK;IAC/D,IAAIA,SAAS,IAAI,IAAI,EAAE;MACrB,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;QACjCP,WAAW,CAACK,OAAO,CAACG,QAAQ,IAAI;UAC9B,MAAMC,OAAO,GAAGF,SAAS,CAACC,QAAQ,CAAC;UACnC,IAAIC,OAAO,IAAI,IAAI,EAAE;YACnB,MAAMC,KAAK,GAAGF,QAAQ,KAAKP,aAAa,GAAG,IAAIO,QAAQ,EAAE,GAAG,EAAE;YAC9DN,OAAO,CAACS,IAAI,CAAC,GAAGL,QAAQ,GAAGI,KAAK,IAAID,OAAO,EAAE,CAAC;UAChD;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLP,OAAO,CAACS,IAAI,CAAC,GAAGL,QAAQ,IAAIC,SAAS,EAAE,CAAC;MAC1C;IACF;EACF,CAAC,CAAC;EACF,OAAOL,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}