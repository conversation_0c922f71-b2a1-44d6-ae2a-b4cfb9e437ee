using XeroxModule.Core.Entities;

namespace XeroxModule.Core.Interfaces
{
    public interface IFileService
    {
        Task<FileUploadResult> UploadFileAsync(Stream fileStream, string fileName, string contentType, long fileSize, int studentId, FileUploadRequest request);
        Task<bool> DeleteFileAsync(int fileUploadId);
        Task<Stream?> GetFileStreamAsync(int fileUploadId);
        Task<byte[]?> GetFileContentAsync(int fileUploadId);
        Task<string?> GetFilePathAsync(int fileUploadId);
        Task<bool> FileExistsAsync(int fileUploadId);
        Task<IEnumerable<FileUpload>> GetStudentFilesAsync(int studentId);
        Task<FileUpload?> GetFileDetailsAsync(int fileUploadId);
        string[] GetAllowedFileTypes();
        long GetMaxFileSize();
        Task<bool> ValidateFileAsync(string fileName, string contentType, long fileSize);
    }
    
    public class FileUploadResult
    {
        public bool Success { get; set; }
        public FileUpload? FileUpload { get; set; }
        public string? ErrorMessage { get; set; }
    }
    
    public class FileUploadRequest
    {
        public string? Remarks { get; set; }
        public int? PreferredXeroxCenterID { get; set; }
        public string PrintType { get; set; } = "Print";
        public int Copies { get; set; } = 1;
        public string ColorType { get; set; } = "BlackWhite";
        public string PaperSize { get; set; } = "A4";
        public string? Description { get; set; }
    }
}
