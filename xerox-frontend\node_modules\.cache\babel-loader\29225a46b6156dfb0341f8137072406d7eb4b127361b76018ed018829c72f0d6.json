{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "map": {"version": 3, "names": ["classNames", "React", "useMemo", "useBootstrapPrefix", "CardHeaderContext", "jsx", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "forwardRef", "bsPrefix", "className", "as", "Component", "props", "ref", "prefix", "contextValue", "cardHeaderBsPrefix", "Provider", "value", "children", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/CardHeader.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EAChDC,QAAQ;EACRC,SAAS;EACT;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGZ,kBAAkB,CAACM,QAAQ,EAAE,aAAa,CAAC;EAC1D,MAAMO,YAAY,GAAGd,OAAO,CAAC,OAAO;IAClCe,kBAAkB,EAAEF;EACtB,CAAC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACb,OAAO,aAAaT,IAAI,CAACF,iBAAiB,CAACc,QAAQ,EAAE;IACnDC,KAAK,EAAEH,YAAY;IACnBI,QAAQ,EAAE,aAAad,IAAI,CAACM,SAAS,EAAE;MACrCE,GAAG,EAAEA,GAAG;MACR,GAAGD,KAAK;MACRH,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAEK,MAAM;IACzC,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,UAAU,CAACc,WAAW,GAAG,YAAY;AACrC,eAAed,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}