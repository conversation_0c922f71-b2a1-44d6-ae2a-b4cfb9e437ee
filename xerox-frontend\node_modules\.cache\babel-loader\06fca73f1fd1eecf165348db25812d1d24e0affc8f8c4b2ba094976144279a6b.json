{"ast": null, "code": "'use strict';\n\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toString = uncurryThis({}.toString);\nvar stringSlice = uncurryThis(''.slice);\nmodule.exports = function (it) {\n  return stringSlice(toString(it), 8, -1);\n};", "map": {"version": 3, "names": ["uncurryThis", "require", "toString", "stringSlice", "slice", "module", "exports", "it"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/core-js-pure/internals/classof-raw.js"], "sourcesContent": ["'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar toString = uncurryThis({}.toString);\nvar stringSlice = uncurryThis(''.slice);\n\nmodule.exports = function (it) {\n  return stringSlice(toString(it), 8, -1);\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AAE/D,IAAIC,QAAQ,GAAGF,WAAW,CAAC,CAAC,CAAC,CAACE,QAAQ,CAAC;AACvC,IAAIC,WAAW,GAAGH,WAAW,CAAC,EAAE,CAACI,KAAK,CAAC;AAEvCC,MAAM,CAACC,OAAO,GAAG,UAAUC,EAAE,EAAE;EAC7B,OAAOJ,WAAW,CAACD,QAAQ,CAACK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACzC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}