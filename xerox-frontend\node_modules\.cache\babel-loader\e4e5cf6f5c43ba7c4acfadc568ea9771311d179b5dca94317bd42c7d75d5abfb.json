{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormCheckLabel = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  htmlFor,\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-check-label');\n  return /*#__PURE__*/_jsx(\"label\", {\n    ...props,\n    ref: ref,\n    htmlFor: htmlFor || controlId,\n    className: classNames(className, bsPrefix)\n  });\n});\nFormCheckLabel.displayName = 'FormCheckLabel';\nexport default FormCheckLabel;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "FormContext", "useBootstrapPrefix", "jsx", "_jsx", "FormCheckLabel", "forwardRef", "bsPrefix", "className", "htmlFor", "props", "ref", "controlId", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/FormCheckLabel.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormCheckLabel = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  htmlFor,\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-check-label');\n  return /*#__PURE__*/_jsx(\"label\", {\n    ...props,\n    ref: ref,\n    htmlFor: htmlFor || controlId,\n    className: classNames(className, bsPrefix)\n  });\n});\nFormCheckLabel.displayName = 'FormCheckLabel';\nexport default FormCheckLabel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EACpDC,QAAQ;EACRC,SAAS;EACTC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJC;EACF,CAAC,GAAGZ,UAAU,CAACC,WAAW,CAAC;EAC3BM,QAAQ,GAAGL,kBAAkB,CAACK,QAAQ,EAAE,kBAAkB,CAAC;EAC3D,OAAO,aAAaH,IAAI,CAAC,OAAO,EAAE;IAChC,GAAGM,KAAK;IACRC,GAAG,EAAEA,GAAG;IACRF,OAAO,EAAEA,OAAO,IAAIG,SAAS;IAC7BJ,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAED,QAAQ;EAC3C,CAAC,CAAC;AACJ,CAAC,CAAC;AACFF,cAAc,CAACQ,WAAW,GAAG,gBAAgB;AAC7C,eAAeR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}