{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const {\n    login,\n    isLoading,\n    error,\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    const success = await login(email, password);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"mt-5\",\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-print fa-3x text-primary mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"h4 text-gray-900 mb-4\",\n                children: \"Welcome Back!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-exclamation-triangle me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 19\n              }, this), error]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form, {\n              onSubmit: handleSubmit,\n              children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-envelope me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 48,\n                    columnNumber: 21\n                  }, this), \"Email Address\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 47,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"email\",\n                  placeholder: \"Enter email\",\n                  value: email,\n                  onChange: e => setEmail(e.target.value),\n                  required: true,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-lock me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 63,\n                    columnNumber: 21\n                  }, this), \"Password\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 62,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"password\",\n                  placeholder: \"Password\",\n                  value: password,\n                  onChange: e => setPassword(e.target.value),\n                  required: true,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 66,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                type: \"submit\",\n                className: \"w-100 mb-3\",\n                disabled: isLoading,\n                children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 84,\n                    columnNumber: 23\n                  }, this), \"Signing In...\"]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-sign-in-alt me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 96,\n                    columnNumber: 23\n                  }, this), \"Sign In\"]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"small\",\n                children: \"Don't have an account? Create Account!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mt-2\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/forgot-password\",\n                className: \"small\",\n                children: \"Forgot Password?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"VujcuGjnyeAqneBEI9MgkkG2/V8=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "email", "setEmail", "password", "setPassword", "login", "isLoading", "error", "user", "navigate", "handleSubmit", "e", "preventDefault", "success", "className", "children", "md", "lg", "Body", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onSubmit", "Group", "Label", "Control", "type", "placeholder", "value", "onChange", "target", "required", "disabled", "as", "animation", "size", "role", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/Login.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Contain<PERSON>, Row, Col, Card, Form, Button, Al<PERSON>, Spinner } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst Login: React.FC = () => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const { login, isLoading, error, user } = useAuth();\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    const success = await login(email, password);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n\n  return (\n    <Container className=\"mt-5\">\n      <Row className=\"justify-content-center\">\n        <Col md={6} lg={4}>\n          <Card className=\"shadow\">\n            <Card.Body className=\"p-4\">\n              <div className=\"text-center mb-4\">\n                <i className=\"fas fa-print fa-3x text-primary mb-3\"></i>\n                <h2 className=\"h4 text-gray-900 mb-4\">Welcome Back!</h2>\n              </div>\n\n              {error && (\n                <Alert variant=\"danger\" className=\"mb-3\">\n                  <i className=\"fas fa-exclamation-triangle me-2\"></i>\n                  {error}\n                </Alert>\n              )}\n\n              <Form onSubmit={handleSubmit}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>\n                    <i className=\"fas fa-envelope me-2\"></i>\n                    Email Address\n                  </Form.Label>\n                  <Form.Control\n                    type=\"email\"\n                    placeholder=\"Enter email\"\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                    required\n                    disabled={isLoading}\n                  />\n                </Form.Group>\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>\n                    <i className=\"fas fa-lock me-2\"></i>\n                    Password\n                  </Form.Label>\n                  <Form.Control\n                    type=\"password\"\n                    placeholder=\"Password\"\n                    value={password}\n                    onChange={(e) => setPassword(e.target.value)}\n                    required\n                    disabled={isLoading}\n                  />\n                </Form.Group>\n\n                <Button \n                  variant=\"primary\" \n                  type=\"submit\" \n                  className=\"w-100 mb-3\"\n                  disabled={isLoading}\n                >\n                  {isLoading ? (\n                    <>\n                      <Spinner\n                        as=\"span\"\n                        animation=\"border\"\n                        size=\"sm\"\n                        role=\"status\"\n                        aria-hidden=\"true\"\n                        className=\"me-2\"\n                      />\n                      Signing In...\n                    </>\n                  ) : (\n                    <>\n                      <i className=\"fas fa-sign-in-alt me-2\"></i>\n                      Sign In\n                    </>\n                  )}\n                </Button>\n              </Form>\n\n              <hr />\n\n              <div className=\"text-center\">\n                <Link to=\"/register\" className=\"small\">\n                  Don't have an account? Create Account!\n                </Link>\n              </div>\n\n              <div className=\"text-center mt-2\">\n                <Link to=\"/forgot-password\" className=\"small\">\n                  Forgot Password?\n                </Link>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACzF,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM;IAAEuB,KAAK;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAGd,OAAO,CAAC,CAAC;EACnD,MAAMe,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9BV,SAAS,CAAC,MAAM;IACd,IAAIyB,IAAI,EAAE;MACRC,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACD,IAAI,EAAEC,QAAQ,CAAC,CAAC;EAEpB,MAAMC,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,MAAMC,OAAO,GAAG,MAAMR,KAAK,CAACJ,KAAK,EAAEE,QAAQ,CAAC;IAC5C,IAAIU,OAAO,EAAE;MACXJ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,oBACEb,OAAA,CAACZ,SAAS;IAAC8B,SAAS,EAAC,MAAM;IAAAC,QAAA,eACzBnB,OAAA,CAACX,GAAG;MAAC6B,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCnB,OAAA,CAACV,GAAG;QAAC8B,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eAChBnB,OAAA,CAACT,IAAI;UAAC2B,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACtBnB,OAAA,CAACT,IAAI,CAAC+B,IAAI;YAACJ,SAAS,EAAC,KAAK;YAAAC,QAAA,gBACxBnB,OAAA;cAAKkB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BnB,OAAA;gBAAGkB,SAAS,EAAC;cAAsC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxD1B,OAAA;gBAAIkB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,EAELf,KAAK,iBACJX,OAAA,CAACN,KAAK;cAACiC,OAAO,EAAC,QAAQ;cAACT,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACtCnB,OAAA;gBAAGkB,SAAS,EAAC;cAAkC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACnDf,KAAK;YAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACR,eAED1B,OAAA,CAACR,IAAI;cAACoC,QAAQ,EAAEd,YAAa;cAAAK,QAAA,gBAC3BnB,OAAA,CAACR,IAAI,CAACqC,KAAK;gBAACX,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BnB,OAAA,CAACR,IAAI,CAACsC,KAAK;kBAAAX,QAAA,gBACTnB,OAAA;oBAAGkB,SAAS,EAAC;kBAAsB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,iBAE1C;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1B,OAAA,CAACR,IAAI,CAACuC,OAAO;kBACXC,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,aAAa;kBACzBC,KAAK,EAAE7B,KAAM;kBACb8B,QAAQ,EAAGpB,CAAC,IAAKT,QAAQ,CAACS,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;kBAC1CG,QAAQ;kBACRC,QAAQ,EAAE5B;gBAAU;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEb1B,OAAA,CAACR,IAAI,CAACqC,KAAK;gBAACX,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1BnB,OAAA,CAACR,IAAI,CAACsC,KAAK;kBAAAX,QAAA,gBACTnB,OAAA;oBAAGkB,SAAS,EAAC;kBAAkB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,YAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb1B,OAAA,CAACR,IAAI,CAACuC,OAAO;kBACXC,IAAI,EAAC,UAAU;kBACfC,WAAW,EAAC,UAAU;kBACtBC,KAAK,EAAE3B,QAAS;kBAChB4B,QAAQ,EAAGpB,CAAC,IAAKP,WAAW,CAACO,CAAC,CAACqB,MAAM,CAACF,KAAK,CAAE;kBAC7CG,QAAQ;kBACRC,QAAQ,EAAE5B;gBAAU;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEb1B,OAAA,CAACP,MAAM;gBACLkC,OAAO,EAAC,SAAS;gBACjBK,IAAI,EAAC,QAAQ;gBACbd,SAAS,EAAC,YAAY;gBACtBoB,QAAQ,EAAE5B,SAAU;gBAAAS,QAAA,EAEnBT,SAAS,gBACRV,OAAA,CAAAE,SAAA;kBAAAiB,QAAA,gBACEnB,OAAA,CAACL,OAAO;oBACN4C,EAAE,EAAC,MAAM;oBACTC,SAAS,EAAC,QAAQ;oBAClBC,IAAI,EAAC,IAAI;oBACTC,IAAI,EAAC,QAAQ;oBACb,eAAY,MAAM;oBAClBxB,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,iBAEJ;gBAAA,eAAE,CAAC,gBAEH1B,OAAA,CAAAE,SAAA;kBAAAiB,QAAA,gBACEnB,OAAA;oBAAGkB,SAAS,EAAC;kBAAyB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,WAE7C;gBAAA,eAAE;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEP1B,OAAA;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAEN1B,OAAA;cAAKkB,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1BnB,OAAA,CAACJ,IAAI;gBAAC+C,EAAE,EAAC,WAAW;gBAACzB,SAAS,EAAC,OAAO;gBAAAC,QAAA,EAAC;cAEvC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN1B,OAAA;cAAKkB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/BnB,OAAA,CAACJ,IAAI;gBAAC+C,EAAE,EAAC,kBAAkB;gBAACzB,SAAS,EAAC,OAAO;gBAAAC,QAAA,EAAC;cAE9C;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACtB,EAAA,CApHID,KAAe;EAAA,QAGuBL,OAAO,EAChCD,WAAW;AAAA;AAAA+C,EAAA,GAJxBzC,KAAe;AAsHrB,eAAeA,KAAK;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}