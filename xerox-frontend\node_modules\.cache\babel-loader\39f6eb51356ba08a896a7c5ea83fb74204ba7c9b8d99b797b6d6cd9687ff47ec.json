{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext({\n  eventKey: ''\n});\ncontext.displayName = 'AccordionItemContext';\nexport default context;", "map": {"version": 3, "names": ["React", "context", "createContext", "eventKey", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/AccordionItemContext.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext({\n  eventKey: ''\n});\ncontext.displayName = 'AccordionItemContext';\nexport default context;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,OAAO,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC;EAC/CC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACFF,OAAO,CAACG,WAAW,GAAG,sBAAsB;AAC5C,eAAeH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}