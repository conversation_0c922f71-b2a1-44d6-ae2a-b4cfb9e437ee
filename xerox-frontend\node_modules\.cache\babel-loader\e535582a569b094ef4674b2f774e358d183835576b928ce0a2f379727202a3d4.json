{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst positionClasses = {\n  'top-start': 'top-0 start-0',\n  'top-center': 'top-0 start-50 translate-middle-x',\n  'top-end': 'top-0 end-0',\n  'middle-start': 'top-50 start-0 translate-middle-y',\n  'middle-center': 'top-50 start-50 translate-middle',\n  'middle-end': 'top-50 end-0 translate-middle-y',\n  'bottom-start': 'bottom-0 start-0',\n  'bottom-center': 'bottom-0 start-50 translate-middle-x',\n  'bottom-end': 'bottom-0 end-0'\n};\nconst ToastContainer = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  position,\n  containerPosition,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'toast-container');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(bsPrefix, position && positionClasses[position], containerPosition && `position-${containerPosition}`, className)\n  });\n});\nToastContainer.displayName = 'ToastContainer';\nexport default ToastContainer;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "positionClasses", "ToastContainer", "forwardRef", "bsPrefix", "position", "containerPosition", "className", "as", "Component", "props", "ref", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/ToastContainer.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst positionClasses = {\n  'top-start': 'top-0 start-0',\n  'top-center': 'top-0 start-50 translate-middle-x',\n  'top-end': 'top-0 end-0',\n  'middle-start': 'top-50 start-0 translate-middle-y',\n  'middle-center': 'top-50 start-50 translate-middle',\n  'middle-end': 'top-50 end-0 translate-middle-y',\n  'bottom-start': 'bottom-0 start-0',\n  'bottom-center': 'bottom-0 start-50 translate-middle-x',\n  'bottom-end': 'bottom-0 end-0'\n};\nconst ToastContainer = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  position,\n  containerPosition,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'toast-container');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(bsPrefix, position && positionClasses[position], containerPosition && `position-${containerPosition}`, className)\n  });\n});\nToastContainer.displayName = 'ToastContainer';\nexport default ToastContainer;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,eAAe,GAAG;EACtB,WAAW,EAAE,eAAe;EAC5B,YAAY,EAAE,mCAAmC;EACjD,SAAS,EAAE,aAAa;EACxB,cAAc,EAAE,mCAAmC;EACnD,eAAe,EAAE,kCAAkC;EACnD,YAAY,EAAE,iCAAiC;EAC/C,cAAc,EAAE,kBAAkB;EAClC,eAAe,EAAE,sCAAsC;EACvD,YAAY,EAAE;AAChB,CAAC;AACD,MAAMC,cAAc,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EACpDC,QAAQ;EACRC,QAAQ;EACRC,iBAAiB;EACjBC,SAAS;EACT;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTP,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,iBAAiB,CAAC;EAC1D,OAAO,aAAaJ,IAAI,CAACS,SAAS,EAAE;IAClCE,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRH,SAAS,EAAEX,UAAU,CAACQ,QAAQ,EAAEC,QAAQ,IAAIJ,eAAe,CAACI,QAAQ,CAAC,EAAEC,iBAAiB,IAAI,YAAYA,iBAAiB,EAAE,EAAEC,SAAS;EACxI,CAAC,CAAC;AACJ,CAAC,CAAC;AACFL,cAAc,CAACU,WAAW,GAAG,gBAAgB;AAC7C,eAAeV,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}