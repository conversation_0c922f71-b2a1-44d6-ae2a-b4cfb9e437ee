{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(localStorage.getItem('token'));\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const API_BASE_URL = 'http://localhost:5007/api'; // Update this to match your API URL\n\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      fetchCurrentUser();\n    }\n  }, [token]);\n  const fetchCurrentUser = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/auth/me`);\n      setUser(response.data);\n    } catch (error) {\n      console.error('Failed to fetch current user:', error);\n      logout();\n    }\n  };\n  const login = async (email, password) => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/auth/login`, {\n        email,\n        password\n      });\n      const {\n        token: newToken,\n        user: userData\n      } = response.data;\n      setToken(newToken);\n      setUser(userData);\n      localStorage.setItem('token', newToken);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n      return true;\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n  };\n  const registerStudent = async data => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/auth/register/student`, data);\n      const {\n        token: newToken,\n        user: userData\n      } = response.data;\n      setToken(newToken);\n      setUser(userData);\n      localStorage.setItem('token', newToken);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n      return true;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      setError(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Registration failed');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const registerXeroxCenter = async data => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/auth/register/xerox-center`, data);\n      const {\n        token: newToken,\n        user: userData\n      } = response.data;\n      setToken(newToken);\n      setUser(userData);\n      localStorage.setItem('token', newToken);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n      return true;\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      setError(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Registration failed');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const value = {\n    user,\n    token,\n    login,\n    logout,\n    registerStudent,\n    registerXeroxCenter,\n    isLoading,\n    error\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"WLkQYycQ3kb4CUbT/ytAzGEILYY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "localStorage", "getItem", "isLoading", "setIsLoading", "error", "setError", "API_BASE_URL", "defaults", "headers", "common", "fetchCurrentUser", "response", "get", "data", "console", "logout", "login", "email", "password", "post", "newToken", "userData", "setItem", "_error$response", "_error$response$data", "message", "removeItem", "registerStudent", "_error$response2", "_error$response2$data", "registerXeroxCenter", "_error$response3", "_error$response3$data", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport axios from 'axios';\n\ninterface User {\n  id: number;\n  username: string;\n  email: string;\n  userType: 'Student' | 'XeroxCenter';\n  isActive: boolean;\n  createdAt: string;\n  lastLoginAt?: string;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  token: string | null;\n  login: (email: string, password: string) => Promise<boolean>;\n  logout: () => void;\n  registerStudent: (data: StudentRegistrationData) => Promise<boolean>;\n  registerXeroxCenter: (data: XeroxCenterRegistrationData) => Promise<boolean>;\n  isLoading: boolean;\n  error: string | null;\n}\n\ninterface StudentRegistrationData {\n  username: string;\n  email: string;\n  password: string;\n  studentNumber: string;\n  firstName: string;\n  lastName: string;\n  phoneNumber?: string;\n  department?: string;\n  year?: number;\n}\n\ninterface XeroxCenterRegistrationData {\n  username: string;\n  email: string;\n  password: string;\n  xeroxCenterName: string;\n  location: string;\n  contactPerson?: string;\n  phoneNumber: string;\n  description?: string;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [token, setToken] = useState<string | null>(localStorage.getItem('token'));\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const API_BASE_URL = 'http://localhost:5007/api'; // Update this to match your API URL\n\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      fetchCurrentUser();\n    }\n  }, [token]);\n\n  const fetchCurrentUser = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/auth/me`);\n      setUser(response.data);\n    } catch (error) {\n      console.error('Failed to fetch current user:', error);\n      logout();\n    }\n  };\n\n  const login = async (email: string, password: string): Promise<boolean> => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      const response = await axios.post(`${API_BASE_URL}/auth/login`, {\n        email,\n        password\n      });\n\n      const { token: newToken, user: userData } = response.data;\n      \n      setToken(newToken);\n      setUser(userData);\n      localStorage.setItem('token', newToken);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n      \n      return true;\n    } catch (error: any) {\n      setError(error.response?.data?.message || 'Login failed');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n  };\n\n  const registerStudent = async (data: StudentRegistrationData): Promise<boolean> => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      const response = await axios.post(`${API_BASE_URL}/auth/register/student`, data);\n      \n      const { token: newToken, user: userData } = response.data;\n      \n      setToken(newToken);\n      setUser(userData);\n      localStorage.setItem('token', newToken);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n      \n      return true;\n    } catch (error: any) {\n      setError(error.response?.data?.message || 'Registration failed');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const registerXeroxCenter = async (data: XeroxCenterRegistrationData): Promise<boolean> => {\n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      const response = await axios.post(`${API_BASE_URL}/auth/register/xerox-center`, data);\n      \n      const { token: newToken, user: userData } = response.data;\n      \n      setToken(newToken);\n      setUser(userData);\n      localStorage.setItem('token', newToken);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${newToken}`;\n      \n      return true;\n    } catch (error: any) {\n      setError(error.response?.data?.message || 'Registration failed');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const value: AuthContextType = {\n    user,\n    token,\n    login,\n    logout,\n    registerStudent,\n    registerXeroxCenter,\n    isLoading,\n    error\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport type { User, StudentRegistrationData, XeroxCenterRegistrationData };\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AACxF,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA8C1B,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACM,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAYpB,OAAO,MAAMI,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAgBkB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;EAChF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAgB,IAAI,CAAC;EAEvD,MAAMwB,YAAY,GAAG,2BAA2B,CAAC,CAAC;;EAElDvB,SAAS,CAAC,MAAM;IACd,IAAIe,KAAK,EAAE;MACTd,KAAK,CAACuB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUX,KAAK,EAAE;MAClEY,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACZ,KAAK,CAAC,CAAC;EAEX,MAAMY,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM3B,KAAK,CAAC4B,GAAG,CAAC,GAAGN,YAAY,UAAU,CAAC;MAC3DT,OAAO,CAACc,QAAQ,CAACE,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDW,MAAM,CAAC,CAAC;IACV;EACF,CAAC;EAED,MAAMC,KAAK,GAAG,MAAAA,CAAOC,KAAa,EAAEC,QAAgB,KAAuB;IACzEf,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAACmC,IAAI,CAAC,GAAGb,YAAY,aAAa,EAAE;QAC9DW,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEpB,KAAK,EAAEsB,QAAQ;QAAExB,IAAI,EAAEyB;MAAS,CAAC,GAAGV,QAAQ,CAACE,IAAI;MAEzDd,QAAQ,CAACqB,QAAQ,CAAC;MAClBvB,OAAO,CAACwB,QAAQ,CAAC;MACjBrB,YAAY,CAACsB,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC;MACvCpC,KAAK,CAACuB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUW,QAAQ,EAAE;MAErE,OAAO,IAAI;IACb,CAAC,CAAC,OAAOhB,KAAU,EAAE;MAAA,IAAAmB,eAAA,EAAAC,oBAAA;MACnBnB,QAAQ,CAAC,EAAAkB,eAAA,GAAAnB,KAAK,CAACO,QAAQ,cAAAY,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsBC,OAAO,KAAI,cAAc,CAAC;MACzD,OAAO,KAAK;IACd,CAAC,SAAS;MACRtB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMY,MAAM,GAAGA,CAAA,KAAM;IACnBlB,OAAO,CAAC,IAAI,CAAC;IACbE,QAAQ,CAAC,IAAI,CAAC;IACdC,YAAY,CAAC0B,UAAU,CAAC,OAAO,CAAC;IAChC,OAAO1C,KAAK,CAACuB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;EACvD,CAAC;EAED,MAAMkB,eAAe,GAAG,MAAOd,IAA6B,IAAuB;IACjFV,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAACmC,IAAI,CAAC,GAAGb,YAAY,wBAAwB,EAAEO,IAAI,CAAC;MAEhF,MAAM;QAAEf,KAAK,EAAEsB,QAAQ;QAAExB,IAAI,EAAEyB;MAAS,CAAC,GAAGV,QAAQ,CAACE,IAAI;MAEzDd,QAAQ,CAACqB,QAAQ,CAAC;MAClBvB,OAAO,CAACwB,QAAQ,CAAC;MACjBrB,YAAY,CAACsB,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC;MACvCpC,KAAK,CAACuB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUW,QAAQ,EAAE;MAErE,OAAO,IAAI;IACb,CAAC,CAAC,OAAOhB,KAAU,EAAE;MAAA,IAAAwB,gBAAA,EAAAC,qBAAA;MACnBxB,QAAQ,CAAC,EAAAuB,gBAAA,GAAAxB,KAAK,CAACO,QAAQ,cAAAiB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,qBAAqB,CAAC;MAChE,OAAO,KAAK;IACd,CAAC,SAAS;MACRtB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM2B,mBAAmB,GAAG,MAAOjB,IAAiC,IAAuB;IACzFV,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAM3B,KAAK,CAACmC,IAAI,CAAC,GAAGb,YAAY,6BAA6B,EAAEO,IAAI,CAAC;MAErF,MAAM;QAAEf,KAAK,EAAEsB,QAAQ;QAAExB,IAAI,EAAEyB;MAAS,CAAC,GAAGV,QAAQ,CAACE,IAAI;MAEzDd,QAAQ,CAACqB,QAAQ,CAAC;MAClBvB,OAAO,CAACwB,QAAQ,CAAC;MACjBrB,YAAY,CAACsB,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC;MACvCpC,KAAK,CAACuB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUW,QAAQ,EAAE;MAErE,OAAO,IAAI;IACb,CAAC,CAAC,OAAOhB,KAAU,EAAE;MAAA,IAAA2B,gBAAA,EAAAC,qBAAA;MACnB3B,QAAQ,CAAC,EAAA0B,gBAAA,GAAA3B,KAAK,CAACO,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlB,IAAI,cAAAmB,qBAAA,uBAApBA,qBAAA,CAAsBP,OAAO,KAAI,qBAAqB,CAAC;MAChE,OAAO,KAAK;IACd,CAAC,SAAS;MACRtB,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM8B,KAAsB,GAAG;IAC7BrC,IAAI;IACJE,KAAK;IACLkB,KAAK;IACLD,MAAM;IACNY,eAAe;IACfG,mBAAmB;IACnB5B,SAAS;IACTE;EACF,CAAC;EAED,oBACElB,OAAA,CAACC,WAAW,CAAC+C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvC,QAAA,EAChCA;EAAQ;IAAAyC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC3C,GAAA,CAxHWF,YAAyC;AAAA8C,EAAA,GAAzC9C,YAAyC;AAAA,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}