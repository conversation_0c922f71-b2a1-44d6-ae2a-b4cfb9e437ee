import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Table, Badge, Alert, Form, Modal, InputGroup } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';

interface PrintJob {
  id: number;
  jobNumber: string;
  fileName: string;
  status: string;
  cost?: number;
  estimatedCompletionTime?: string;
  studentName: string;
  studentEmail: string;
  printType: string;
  copies: number;
  colorType: string;
  paperSize: string;
  remarks?: string;
  created: string;
}

const XeroxCenterDashboard: React.FC = () => {
  const { user } = useAuth();
  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);
  const [selectedJob, setSelectedJob] = useState<PrintJob | null>(null);
  const [showQuoteModal, setShowQuoteModal] = useState(false);
  const [quoteData, setQuoteData] = useState({
    cost: '',
    estimatedHours: '',
    notes: ''
  });
  const [filterStatus, setFilterStatus] = useState('all');

  useEffect(() => {
    // Mock data - replace with actual API calls
    setPrintJobs([
      {
        id: 1,
        jobNumber: 'JOB-001',
        fileName: 'Assignment1.pdf',
        status: 'Requested',
        studentName: 'John Doe',
        studentEmail: '<EMAIL>',
        printType: 'Print',
        copies: 5,
        colorType: 'BlackWhite',
        paperSize: 'A4',
        remarks: 'Please print double-sided',
        created: '2024-01-15T10:00:00'
      },
      {
        id: 2,
        jobNumber: 'JOB-002',
        fileName: 'Thesis_Chapter1.docx',
        status: 'InProgress',
        cost: 45.00,
        estimatedCompletionTime: '2024-01-15T16:00:00',
        studentName: 'Jane Smith',
        studentEmail: '<EMAIL>',
        printType: 'Print',
        copies: 1,
        colorType: 'BlackWhite',
        paperSize: 'A4',
        created: '2024-01-14T09:00:00'
      },
      {
        id: 3,
        jobNumber: 'JOB-003',
        fileName: 'Presentation.pptx',
        status: 'WaitingConfirmation',
        cost: 25.50,
        studentName: 'Mike Johnson',
        studentEmail: '<EMAIL>',
        printType: 'Print',
        copies: 10,
        colorType: 'Color',
        paperSize: 'A4',
        created: '2024-01-15T08:30:00'
      }
    ]);
  }, []);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'Requested': { variant: 'secondary', icon: 'clock' },
      'UnderReview': { variant: 'info', icon: 'eye' },
      'Quoted': { variant: 'warning', icon: 'dollar-sign' },
      'WaitingConfirmation': { variant: 'warning', icon: 'hourglass-half' },
      'Confirmed': { variant: 'primary', icon: 'check' },
      'InProgress': { variant: 'info', icon: 'cog' },
      'Completed': { variant: 'success', icon: 'check-circle' },
      'Delivered': { variant: 'success', icon: 'truck' },
      'Rejected': { variant: 'danger', icon: 'times' },
      'Cancelled': { variant: 'dark', icon: 'ban' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'secondary', icon: 'question' };
    
    return (
      <Badge bg={config.variant}>
        <i className={`fas fa-${config.icon} me-1`}></i>
        {status}
      </Badge>
    );
  };

  const handleQuoteSubmit = () => {
    if (selectedJob) {
      const estimatedCompletion = new Date();
      estimatedCompletion.setHours(estimatedCompletion.getHours() + parseInt(quoteData.estimatedHours));
      
      // Mock API call to update job with quote
      console.log('Submitting quote:', {
        jobId: selectedJob.id,
        cost: parseFloat(quoteData.cost),
        estimatedCompletion,
        notes: quoteData.notes
      });
      
      // Update local state
      setPrintJobs(prev => prev.map(job => 
        job.id === selectedJob.id 
          ? { ...job, status: 'Quoted', cost: parseFloat(quoteData.cost), estimatedCompletionTime: estimatedCompletion.toISOString() }
          : job
      ));
    }
    
    setShowQuoteModal(false);
    setSelectedJob(null);
    setQuoteData({ cost: '', estimatedHours: '', notes: '' });
  };

  const handleStatusUpdate = (jobId: number, newStatus: string) => {
    // Mock API call to update job status
    console.log('Updating job status:', { jobId, newStatus });
    
    setPrintJobs(prev => prev.map(job => 
      job.id === jobId ? { ...job, status: newStatus } : job
    ));
  };

  const filteredJobs = filterStatus === 'all' 
    ? printJobs 
    : printJobs.filter(job => job.status === filterStatus);

  const stats = {
    total: printJobs.length,
    pending: printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation'].includes(job.status)).length,
    inProgress: printJobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length,
    completed: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,
    revenue: printJobs.reduce((sum, job) => sum + (job.cost || 0), 0)
  };

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h2>
            <i className="fas fa-store me-2"></i>
            Xerox Center Dashboard
          </h2>
          <p className="text-muted">Welcome back, {user?.username}!</p>
        </Col>
      </Row>

      {/* Statistics Cards */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center border-left-primary">
            <Card.Body>
              <i className="fas fa-file-alt fa-2x text-primary mb-2"></i>
              <h5>Total Jobs</h5>
              <h3 className="text-primary">{stats.total}</h3>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center border-left-warning">
            <Card.Body>
              <i className="fas fa-clock fa-2x text-warning mb-2"></i>
              <h5>Pending</h5>
              <h3 className="text-warning">{stats.pending}</h3>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center border-left-info">
            <Card.Body>
              <i className="fas fa-cog fa-2x text-info mb-2"></i>
              <h5>In Progress</h5>
              <h3 className="text-info">{stats.inProgress}</h3>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center border-left-success">
            <Card.Body>
              <i className="fas fa-dollar-sign fa-2x text-success mb-2"></i>
              <h5>Revenue</h5>
              <h3 className="text-success">${stats.revenue.toFixed(2)}</h3>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Job Queue */}
      <Card>
        <Card.Header className="d-flex justify-content-between align-items-center">
          <h5 className="mb-0">
            <i className="fas fa-tasks me-2"></i>
            Job Queue
          </h5>
          <Form.Select 
            style={{ width: 'auto' }}
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">All Jobs</option>
            <option value="Requested">Requested</option>
            <option value="UnderReview">Under Review</option>
            <option value="Quoted">Quoted</option>
            <option value="WaitingConfirmation">Waiting Confirmation</option>
            <option value="Confirmed">Confirmed</option>
            <option value="InProgress">In Progress</option>
            <option value="Completed">Completed</option>
          </Form.Select>
        </Card.Header>
        <Card.Body>
          {filteredJobs.length > 0 ? (
            <Table responsive hover>
              <thead>
                <tr>
                  <th>Job #</th>
                  <th>Student</th>
                  <th>File</th>
                  <th>Details</th>
                  <th>Status</th>
                  <th>Cost</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredJobs.map(job => (
                  <tr key={job.id}>
                    <td>
                      <strong>{job.jobNumber}</strong>
                      <br />
                      <small className="text-muted">
                        {new Date(job.created).toLocaleDateString()}
                      </small>
                    </td>
                    <td>
                      <div>
                        <strong>{job.studentName}</strong>
                        <br />
                        <small className="text-muted">{job.studentEmail}</small>
                      </div>
                    </td>
                    <td>
                      <i className="fas fa-file-pdf me-2 text-danger"></i>
                      {job.fileName}
                    </td>
                    <td>
                      <div className="small">
                        <div><strong>Type:</strong> {job.printType}</div>
                        <div><strong>Copies:</strong> {job.copies}</div>
                        <div><strong>Color:</strong> {job.colorType}</div>
                        <div><strong>Size:</strong> {job.paperSize}</div>
                        {job.remarks && (
                          <div><strong>Remarks:</strong> {job.remarks}</div>
                        )}
                      </div>
                    </td>
                    <td>{getStatusBadge(job.status)}</td>
                    <td>
                      {job.cost ? `$${job.cost.toFixed(2)}` : '-'}
                    </td>
                    <td>
                      <div className="btn-group-vertical" role="group">
                        {job.status === 'Requested' && (
                          <>
                            <Button 
                              variant="outline-primary" 
                              size="sm"
                              onClick={() => {
                                setSelectedJob(job);
                                setShowQuoteModal(true);
                              }}
                            >
                              <i className="fas fa-dollar-sign me-1"></i>
                              Quote
                            </Button>
                            <Button 
                              variant="outline-danger" 
                              size="sm"
                              onClick={() => handleStatusUpdate(job.id, 'Rejected')}
                            >
                              <i className="fas fa-times me-1"></i>
                              Reject
                            </Button>
                          </>
                        )}
                        
                        {job.status === 'Confirmed' && (
                          <Button 
                            variant="outline-info" 
                            size="sm"
                            onClick={() => handleStatusUpdate(job.id, 'InProgress')}
                          >
                            <i className="fas fa-play me-1"></i>
                            Start
                          </Button>
                        )}
                        
                        {job.status === 'InProgress' && (
                          <Button 
                            variant="outline-success" 
                            size="sm"
                            onClick={() => handleStatusUpdate(job.id, 'Completed')}
                          >
                            <i className="fas fa-check me-1"></i>
                            Complete
                          </Button>
                        )}
                        
                        {job.status === 'Completed' && (
                          <Button 
                            variant="outline-success" 
                            size="sm"
                            onClick={() => handleStatusUpdate(job.id, 'Delivered')}
                          >
                            <i className="fas fa-truck me-1"></i>
                            Deliver
                          </Button>
                        )}
                        
                        <Button variant="outline-secondary" size="sm">
                          <i className="fas fa-comment"></i>
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          ) : (
            <Alert variant="info">
              <i className="fas fa-info-circle me-2"></i>
              No jobs found for the selected filter.
            </Alert>
          )}
        </Card.Body>
      </Card>

      {/* Quote Modal */}
      <Modal show={showQuoteModal} onHide={() => setShowQuoteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-dollar-sign me-2"></i>
            Provide Quote - {selectedJob?.jobNumber}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedJob && (
            <>
              <div className="mb-3 p-3 bg-light rounded">
                <h6>Job Details:</h6>
                <div className="row">
                  <div className="col-6">
                    <strong>File:</strong> {selectedJob.fileName}<br />
                    <strong>Type:</strong> {selectedJob.printType}<br />
                    <strong>Copies:</strong> {selectedJob.copies}
                  </div>
                  <div className="col-6">
                    <strong>Color:</strong> {selectedJob.colorType}<br />
                    <strong>Size:</strong> {selectedJob.paperSize}<br />
                    <strong>Student:</strong> {selectedJob.studentName}
                  </div>
                </div>
                {selectedJob.remarks && (
                  <div className="mt-2">
                    <strong>Remarks:</strong> {selectedJob.remarks}
                  </div>
                )}
              </div>

              <Form>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Cost ($)</Form.Label>
                      <InputGroup>
                        <InputGroup.Text>$</InputGroup.Text>
                        <Form.Control
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          value={quoteData.cost}
                          onChange={(e) => setQuoteData(prev => ({ ...prev, cost: e.target.value }))}
                          required
                        />
                      </InputGroup>
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Estimated Hours</Form.Label>
                      <Form.Control
                        type="number"
                        min="1"
                        placeholder="Hours to complete"
                        value={quoteData.estimatedHours}
                        onChange={(e) => setQuoteData(prev => ({ ...prev, estimatedHours: e.target.value }))}
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-3">
                  <Form.Label>Notes (Optional)</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    placeholder="Any additional notes for the student..."
                    value={quoteData.notes}
                    onChange={(e) => setQuoteData(prev => ({ ...prev, notes: e.target.value }))}
                  />
                </Form.Group>
              </Form>
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowQuoteModal(false)}>
            Cancel
          </Button>
          <Button 
            variant="primary" 
            onClick={handleQuoteSubmit}
            disabled={!quoteData.cost || !quoteData.estimatedHours}
          >
            <i className="fas fa-paper-plane me-2"></i>
            Send Quote
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default XeroxCenterDashboard;
