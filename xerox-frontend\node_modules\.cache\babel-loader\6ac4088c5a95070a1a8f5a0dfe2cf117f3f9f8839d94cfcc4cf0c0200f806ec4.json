{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownDivider = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'hr',\n  role = 'separator',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'dropdown-divider');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    role: role,\n    ...props\n  });\n});\nDropdownDivider.displayName = 'DropdownDivider';\nexport default DropdownDivider;", "map": {"version": 3, "names": ["React", "classNames", "useBootstrapPrefix", "jsx", "_jsx", "DropdownDivider", "forwardRef", "className", "bsPrefix", "as", "Component", "role", "props", "ref", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/DropdownDivider.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownDivider = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'hr',\n  role = 'separator',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'dropdown-divider');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    role: role,\n    ...props\n  });\n});\nDropdownDivider.displayName = 'DropdownDivider';\nexport default DropdownDivider;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,eAAe,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EACrDC,SAAS;EACTC,QAAQ;EACRC,EAAE,EAAEC,SAAS,GAAG,IAAI;EACpBC,IAAI,GAAG,WAAW;EAClB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTL,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,kBAAkB,CAAC;EAC3D,OAAO,aAAaJ,IAAI,CAACM,SAAS,EAAE;IAClCG,GAAG,EAAEA,GAAG;IACRN,SAAS,EAAEN,UAAU,CAACM,SAAS,EAAEC,QAAQ,CAAC;IAC1CG,IAAI,EAAEA,IAAI;IACV,GAAGC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFP,eAAe,CAACS,WAAW,GAAG,iBAAiB;AAC/C,eAAeT,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}