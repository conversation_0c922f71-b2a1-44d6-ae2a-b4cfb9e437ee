{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const DEFAULT_BREAKPOINTS = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];\nexport const DEFAULT_MIN_BREAKPOINT = 'xs';\nconst ThemeContext = /*#__PURE__*/React.createContext({\n  prefixes: {},\n  breakpoints: DEFAULT_BREAKPOINTS,\n  minBreakpoint: DEFAULT_MIN_BREAKPOINT\n});\nconst {\n  Consumer,\n  Provider\n} = ThemeContext;\nfunction ThemeProvider({\n  prefixes = {},\n  breakpoints = DEFAULT_BREAKPOINTS,\n  minBreakpoint = DEFAULT_MIN_BREAKPOINT,\n  dir,\n  children\n}) {\n  const contextValue = useMemo(() => ({\n    prefixes: {\n      ...prefixes\n    },\n    breakpoints,\n    minBreakpoint,\n    dir\n  }), [prefixes, breakpoints, minBreakpoint, dir]);\n  return /*#__PURE__*/_jsx(Provider, {\n    value: contextValue,\n    children: children\n  });\n}\nexport function useBootstrapPrefix(prefix, defaultPrefix) {\n  const {\n    prefixes\n  } = useContext(ThemeContext);\n  return prefix || prefixes[defaultPrefix] || defaultPrefix;\n}\nexport function useBootstrapBreakpoints() {\n  const {\n    breakpoints\n  } = useContext(ThemeContext);\n  return breakpoints;\n}\nexport function useBootstrapMinBreakpoint() {\n  const {\n    minBreakpoint\n  } = useContext(ThemeContext);\n  return minBreakpoint;\n}\nexport function useIsRTL() {\n  const {\n    dir\n  } = useContext(ThemeContext);\n  return dir === 'rtl';\n}\nfunction createBootstrapComponent(Component, opts) {\n  if (typeof opts === 'string') opts = {\n    prefix: opts\n  };\n  const isClassy = Component.prototype && Component.prototype.isReactComponent;\n  // If it's a functional component make sure we don't break it with a ref\n  const {\n    prefix,\n    forwardRefAs = isClassy ? 'ref' : 'innerRef'\n  } = opts;\n  const Wrapped = /*#__PURE__*/React.forwardRef(({\n    ...props\n  }, ref) => {\n    props[forwardRefAs] = ref;\n    const bsPrefix = useBootstrapPrefix(props.bsPrefix, prefix);\n    return /*#__PURE__*/_jsx(Component, {\n      ...props,\n      bsPrefix: bsPrefix\n    });\n  });\n  Wrapped.displayName = `Bootstrap(${Component.displayName || Component.name})`;\n  return Wrapped;\n}\nexport { createBootstrapComponent, Consumer as ThemeConsumer };\nexport default ThemeProvider;", "map": {"version": 3, "names": ["React", "useContext", "useMemo", "jsx", "_jsx", "DEFAULT_BREAKPOINTS", "DEFAULT_MIN_BREAKPOINT", "ThemeContext", "createContext", "prefixes", "breakpoints", "minBreakpoint", "Consumer", "Provider", "ThemeProvider", "dir", "children", "contextValue", "value", "useBootstrapPrefix", "prefix", "defaultPrefix", "useBootstrapBreakpoints", "useBootstrapMinBreakpoint", "useIsRTL", "createBootstrapComponent", "Component", "opts", "isClassy", "prototype", "isReactComponent", "forwardRefAs", "Wrapped", "forwardRef", "props", "ref", "bsPrefix", "displayName", "name", "ThemeConsumer"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/ThemeProvider.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const DEFAULT_BREAKPOINTS = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];\nexport const DEFAULT_MIN_BREAKPOINT = 'xs';\nconst ThemeContext = /*#__PURE__*/React.createContext({\n  prefixes: {},\n  breakpoints: DEFAULT_BREAKPOINTS,\n  minBreakpoint: DEFAULT_MIN_BREAKPOINT\n});\nconst {\n  Consumer,\n  Provider\n} = ThemeContext;\nfunction ThemeProvider({\n  prefixes = {},\n  breakpoints = DEFAULT_BREAKPOINTS,\n  minBreakpoint = DEFAULT_MIN_BREAKPOINT,\n  dir,\n  children\n}) {\n  const contextValue = useMemo(() => ({\n    prefixes: {\n      ...prefixes\n    },\n    breakpoints,\n    minBreakpoint,\n    dir\n  }), [prefixes, breakpoints, minBreakpoint, dir]);\n  return /*#__PURE__*/_jsx(Provider, {\n    value: contextValue,\n    children: children\n  });\n}\nexport function useBootstrapPrefix(prefix, defaultPrefix) {\n  const {\n    prefixes\n  } = useContext(ThemeContext);\n  return prefix || prefixes[defaultPrefix] || defaultPrefix;\n}\nexport function useBootstrapBreakpoints() {\n  const {\n    breakpoints\n  } = useContext(ThemeContext);\n  return breakpoints;\n}\nexport function useBootstrapMinBreakpoint() {\n  const {\n    minBreakpoint\n  } = useContext(ThemeContext);\n  return minBreakpoint;\n}\nexport function useIsRTL() {\n  const {\n    dir\n  } = useContext(ThemeContext);\n  return dir === 'rtl';\n}\nfunction createBootstrapComponent(Component, opts) {\n  if (typeof opts === 'string') opts = {\n    prefix: opts\n  };\n  const isClassy = Component.prototype && Component.prototype.isReactComponent;\n  // If it's a functional component make sure we don't break it with a ref\n  const {\n    prefix,\n    forwardRefAs = isClassy ? 'ref' : 'innerRef'\n  } = opts;\n  const Wrapped = /*#__PURE__*/React.forwardRef(({\n    ...props\n  }, ref) => {\n    props[forwardRefAs] = ref;\n    const bsPrefix = useBootstrapPrefix(props.bsPrefix, prefix);\n    return /*#__PURE__*/_jsx(Component, {\n      ...props,\n      bsPrefix: bsPrefix\n    });\n  });\n  Wrapped.displayName = `Bootstrap(${Component.displayName || Component.name})`;\n  return Wrapped;\n}\nexport { createBootstrapComponent, Consumer as ThemeConsumer };\nexport default ThemeProvider;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,mBAAmB,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACxE,OAAO,MAAMC,sBAAsB,GAAG,IAAI;AAC1C,MAAMC,YAAY,GAAG,aAAaP,KAAK,CAACQ,aAAa,CAAC;EACpDC,QAAQ,EAAE,CAAC,CAAC;EACZC,WAAW,EAAEL,mBAAmB;EAChCM,aAAa,EAAEL;AACjB,CAAC,CAAC;AACF,MAAM;EACJM,QAAQ;EACRC;AACF,CAAC,GAAGN,YAAY;AAChB,SAASO,aAAaA,CAAC;EACrBL,QAAQ,GAAG,CAAC,CAAC;EACbC,WAAW,GAAGL,mBAAmB;EACjCM,aAAa,GAAGL,sBAAsB;EACtCS,GAAG;EACHC;AACF,CAAC,EAAE;EACD,MAAMC,YAAY,GAAGf,OAAO,CAAC,OAAO;IAClCO,QAAQ,EAAE;MACR,GAAGA;IACL,CAAC;IACDC,WAAW;IACXC,aAAa;IACbI;EACF,CAAC,CAAC,EAAE,CAACN,QAAQ,EAAEC,WAAW,EAAEC,aAAa,EAAEI,GAAG,CAAC,CAAC;EAChD,OAAO,aAAaX,IAAI,CAACS,QAAQ,EAAE;IACjCK,KAAK,EAAED,YAAY;IACnBD,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACA,OAAO,SAASG,kBAAkBA,CAACC,MAAM,EAAEC,aAAa,EAAE;EACxD,MAAM;IACJZ;EACF,CAAC,GAAGR,UAAU,CAACM,YAAY,CAAC;EAC5B,OAAOa,MAAM,IAAIX,QAAQ,CAACY,aAAa,CAAC,IAAIA,aAAa;AAC3D;AACA,OAAO,SAASC,uBAAuBA,CAAA,EAAG;EACxC,MAAM;IACJZ;EACF,CAAC,GAAGT,UAAU,CAACM,YAAY,CAAC;EAC5B,OAAOG,WAAW;AACpB;AACA,OAAO,SAASa,yBAAyBA,CAAA,EAAG;EAC1C,MAAM;IACJZ;EACF,CAAC,GAAGV,UAAU,CAACM,YAAY,CAAC;EAC5B,OAAOI,aAAa;AACtB;AACA,OAAO,SAASa,QAAQA,CAAA,EAAG;EACzB,MAAM;IACJT;EACF,CAAC,GAAGd,UAAU,CAACM,YAAY,CAAC;EAC5B,OAAOQ,GAAG,KAAK,KAAK;AACtB;AACA,SAASU,wBAAwBA,CAACC,SAAS,EAAEC,IAAI,EAAE;EACjD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAEA,IAAI,GAAG;IACnCP,MAAM,EAAEO;EACV,CAAC;EACD,MAAMC,QAAQ,GAAGF,SAAS,CAACG,SAAS,IAAIH,SAAS,CAACG,SAAS,CAACC,gBAAgB;EAC5E;EACA,MAAM;IACJV,MAAM;IACNW,YAAY,GAAGH,QAAQ,GAAG,KAAK,GAAG;EACpC,CAAC,GAAGD,IAAI;EACR,MAAMK,OAAO,GAAG,aAAahC,KAAK,CAACiC,UAAU,CAAC,CAAC;IAC7C,GAAGC;EACL,CAAC,EAAEC,GAAG,KAAK;IACTD,KAAK,CAACH,YAAY,CAAC,GAAGI,GAAG;IACzB,MAAMC,QAAQ,GAAGjB,kBAAkB,CAACe,KAAK,CAACE,QAAQ,EAAEhB,MAAM,CAAC;IAC3D,OAAO,aAAahB,IAAI,CAACsB,SAAS,EAAE;MAClC,GAAGQ,KAAK;MACRE,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;EACFJ,OAAO,CAACK,WAAW,GAAG,aAAaX,SAAS,CAACW,WAAW,IAAIX,SAAS,CAACY,IAAI,GAAG;EAC7E,OAAON,OAAO;AAChB;AACA,SAASP,wBAAwB,EAAEb,QAAQ,IAAI2B,aAAa;AAC5D,eAAezB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}