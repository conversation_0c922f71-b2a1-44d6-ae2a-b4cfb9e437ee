{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ButtonToolbar = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  role = 'toolbar',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn-toolbar');\n  return /*#__PURE__*/_jsx(\"div\", {\n    ...props,\n    ref: ref,\n    className: classNames(className, prefix),\n    role: role\n  });\n});\nButtonToolbar.displayName = 'ButtonToolbar';\nexport default ButtonToolbar;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "ButtonToolbar", "forwardRef", "bsPrefix", "className", "role", "props", "ref", "prefix", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/ButtonToolbar.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ButtonToolbar = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  role = 'toolbar',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'btn-toolbar');\n  return /*#__PURE__*/_jsx(\"div\", {\n    ...props,\n    ref: ref,\n    className: classNames(className, prefix),\n    role: role\n  });\n});\nButtonToolbar.displayName = 'ButtonToolbar';\nexport default ButtonToolbar;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EACnDC,QAAQ;EACRC,SAAS;EACTC,IAAI,GAAG,SAAS;EAChB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGV,kBAAkB,CAACK,QAAQ,EAAE,aAAa,CAAC;EAC1D,OAAO,aAAaH,IAAI,CAAC,KAAK,EAAE;IAC9B,GAAGM,KAAK;IACRC,GAAG,EAAEA,GAAG;IACRH,SAAS,EAAER,UAAU,CAACQ,SAAS,EAAEI,MAAM,CAAC;IACxCH,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,aAAa,CAACQ,WAAW,GAAG,eAAe;AAC3C,eAAeR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}