{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"XeroxModule.Application/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "5.0.17", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "System.IdentityModel.Tokens.Jwt": "8.12.0", "XeroxModule.Core": "1.0.0"}, "runtime": {"XeroxModule.Application.dll": {}}}, "Microsoft.AspNetCore.Http.Features/5.0.17": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6", "System.IO.Pipelines": "5.0.2"}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.1722.21507"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.IdentityModel.Abstractions/8.12.0": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "8.12.0.60603"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.12.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.12.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "********", "fileVersion": "8.12.0.60603"}}}, "Microsoft.IdentityModel.Logging/8.12.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.12.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "********", "fileVersion": "8.12.0.60603"}}}, "Microsoft.IdentityModel.Tokens/8.12.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.IdentityModel.Logging": "8.12.0"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "8.12.0.60603"}}}, "System.IdentityModel.Tokens.Jwt/8.12.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.12.0", "Microsoft.IdentityModel.Tokens": "8.12.0"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "********", "fileVersion": "8.12.0.60603"}}}, "System.IO.Pipelines/5.0.2": {}, "XeroxModule.Core/1.0.0": {"runtime": {"XeroxModule.Core.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}}}, "libraries": {"XeroxModule.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features/5.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-3jG2xS+dx8DDCGV/F+STdPTg89lX3ao3dF/VEPvJaz3wzBIjuadipTtYNEXDIVuOPZwb6jdmhrX9jkzOIBm5cw==", "path": "microsoft.aspnetcore.http.features/5.0.17", "hashPath": "microsoft.aspnetcore.http.features.5.0.17.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-V7fHMFpfzvx7twWMV3jrf3OFVmYn3QhUYtvfMRD9yUPs9gxnQSaRMZh5NzCsnW3ZZ80J09EE7yyjM71y9JC6hQ==", "path": "microsoft.identitymodel.abstractions/8.12.0", "hashPath": "microsoft.identitymodel.abstractions.8.12.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-gOup2SmdwaXooLR0POKfrkyrw+R+G8nc8Nk80TL0196kFQK7Bg7Qr4x3jvOCm3TR8QLqU8cVpSVqBLtUaosPEg==", "path": "microsoft.identitymodel.jsonwebtokens/8.12.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.12.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-IakwNWUTy5RlcDcKwtL5TyfDLZmA8FFnSDhfr+wfGGPMON9GkpkUY8NakIJjdy6mv6C1lM/Jkn3IuaeS9MXesA==", "path": "microsoft.identitymodel.logging/8.12.0", "hashPath": "microsoft.identitymodel.logging.8.12.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-WCU3wv5sioX5hhp3oHw8sCdygnfZJtRKJGCg+AckP6nbp0QGKK3VohTrxIF0dpuziLAPg57CPfS9X8UERroKxA==", "path": "microsoft.identitymodel.tokens/8.12.0", "hashPath": "microsoft.identitymodel.tokens.8.12.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-JpkST6AQlxrXXQ05jVNqoPsU9fjIfERJdCWMxIBWzGhuNH4q/TkP5suPdlNFtHhIN+ngWQ8rmaCNY35EhACHwg==", "path": "system.identitymodel.tokens.jwt/8.12.0", "hashPath": "system.identitymodel.tokens.jwt.8.12.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Iew+dfa6FFiyvWBdRmXApixRY1db+beyutpIck4SOSe0NLM8FD/7AD54MscqVLhvfSMLHO7KadjTRT7fqxOGTA==", "path": "system.io.pipelines/5.0.2", "hashPath": "system.io.pipelines.5.0.2.nupkg.sha512"}, "XeroxModule.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}