{"ast": null, "code": "import { useRef, useEffect } from 'react';\n\n/**\n * Track whether a component is current mounted. Generally less preferable than\n * properlly canceling effects so they don't run after a component is unmounted,\n * but helpful in cases where that isn't feasible, such as a `Promise` resolution.\n *\n * @returns a function that returns the current isMounted state of the component\n *\n * ```ts\n * const [data, setData] = useState(null)\n * const isMounted = useMounted()\n *\n * useEffect(() => {\n *   fetchdata().then((newData) => {\n *      if (isMounted()) {\n *        setData(newData);\n *      }\n *   })\n * })\n * ```\n */\nexport default function useMounted() {\n  const mounted = useRef(true);\n  const isMounted = useRef(() => mounted.current);\n  useEffect(() => {\n    mounted.current = true;\n    return () => {\n      mounted.current = false;\n    };\n  }, []);\n  return isMounted.current;\n}", "map": {"version": 3, "names": ["useRef", "useEffect", "useMounted", "mounted", "isMounted", "current"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useMounted.js"], "sourcesContent": ["import { useRef, useEffect } from 'react';\n\n/**\n * Track whether a component is current mounted. Generally less preferable than\n * properlly canceling effects so they don't run after a component is unmounted,\n * but helpful in cases where that isn't feasible, such as a `Promise` resolution.\n *\n * @returns a function that returns the current isMounted state of the component\n *\n * ```ts\n * const [data, setData] = useState(null)\n * const isMounted = useMounted()\n *\n * useEffect(() => {\n *   fetchdata().then((newData) => {\n *      if (isMounted()) {\n *        setData(newData);\n *      }\n *   })\n * })\n * ```\n */\nexport default function useMounted() {\n  const mounted = useRef(true);\n  const isMounted = useRef(() => mounted.current);\n  useEffect(() => {\n    mounted.current = true;\n    return () => {\n      mounted.current = false;\n    };\n  }, []);\n  return isMounted.current;\n}"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,QAAQ,OAAO;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAAA,EAAG;EACnC,MAAMC,OAAO,GAAGH,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMI,SAAS,GAAGJ,MAAM,CAAC,MAAMG,OAAO,CAACE,OAAO,CAAC;EAC/CJ,SAAS,CAAC,MAAM;IACdE,OAAO,CAACE,OAAO,GAAG,IAAI;IACtB,OAAO,MAAM;MACXF,OAAO,CAACE,OAAO,GAAG,KAAK;IACzB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOD,SAAS,CAACC,OAAO;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}