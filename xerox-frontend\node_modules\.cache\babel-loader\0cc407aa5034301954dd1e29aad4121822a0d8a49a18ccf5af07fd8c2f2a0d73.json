{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormContext from './FormContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormRange = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  id,\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-range');\n  return /*#__PURE__*/_jsx(\"input\", {\n    ...props,\n    type: \"range\",\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    id: id || controlId\n  });\n});\nFormRange.displayName = 'FormRange';\nexport default FormRange;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useBootstrapPrefix", "FormContext", "jsx", "_jsx", "FormRange", "forwardRef", "bsPrefix", "className", "id", "props", "ref", "controlId", "type", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/FormRange.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport FormContext from './FormContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormRange = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  id,\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-range');\n  return /*#__PURE__*/_jsx(\"input\", {\n    ...props,\n    type: \"range\",\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    id: id || controlId\n  });\n});\nFormRange.displayName = 'FormRange';\nexport default FormRange;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EAC/CC,QAAQ;EACRC,SAAS;EACTC,EAAE;EACF,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJC;EACF,CAAC,GAAGZ,UAAU,CAACE,WAAW,CAAC;EAC3BK,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,YAAY,CAAC;EACrD,OAAO,aAAaH,IAAI,CAAC,OAAO,EAAE;IAChC,GAAGM,KAAK;IACRG,IAAI,EAAE,OAAO;IACbF,GAAG,EAAEA,GAAG;IACRH,SAAS,EAAEV,UAAU,CAACU,SAAS,EAAED,QAAQ,CAAC;IAC1CE,EAAE,EAAEA,EAAE,IAAIG;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFP,SAAS,CAACS,WAAW,GAAG,WAAW;AACnC,eAAeT,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}