/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export declare const makeInvalidReturnValueError: () => string;
export declare const makeInvalidSourceMapWarning: (filename: string, transformPath: string) => string;
export declare const makeInvalidSyncTransformerError: (transformPath: string) => string;
export declare const makeInvalidTransformerError: (transformPath: string) => string;
