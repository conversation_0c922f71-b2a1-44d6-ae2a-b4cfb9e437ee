{"ast": null, "code": "import PropTypes from 'prop-types';\nimport Tab<PERSON>ontainer from './TabContainer';\nimport TabContent from './TabContent';\nimport TabPane from './TabPane';\nconst propTypes = {\n  eventKey: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  /**\n   * Content for the tab title.\n   */\n  title: PropTypes.node.isRequired,\n  /**\n   * The disabled state of the tab.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Class to pass to the underlying nav link.\n   */\n  tabClassName: PropTypes.string,\n  /**\n   * Object containing attributes to pass to underlying nav link.\n   */\n  tabAttrs: PropTypes.object\n};\nconst Tab = () => {\n  throw new Error('ReactBootstrap: The `Tab` component is not meant to be rendered! ' + \"It's an abstract component that is only valid as a direct Child of the `Tabs` Component. \" + 'For custom tabs components use TabPane and TabsContainer directly');\n};\nTab.propTypes = propTypes;\nexport default Object.assign(Tab, {\n  Container: TabContainer,\n  Content: TabContent,\n  Pane: TabPane\n});", "map": {"version": 3, "names": ["PropTypes", "TabContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TabPane", "propTypes", "eventKey", "oneOfType", "string", "number", "title", "node", "isRequired", "disabled", "bool", "tabClassName", "tabAttrs", "object", "Tab", "Error", "Object", "assign", "Container", "Content", "Pane"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/Tab.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport Tab<PERSON>ontainer from './TabContainer';\nimport TabContent from './TabContent';\nimport TabPane from './TabPane';\nconst propTypes = {\n  eventKey: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),\n  /**\n   * Content for the tab title.\n   */\n  title: PropTypes.node.isRequired,\n  /**\n   * The disabled state of the tab.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Class to pass to the underlying nav link.\n   */\n  tabClassName: PropTypes.string,\n  /**\n   * Object containing attributes to pass to underlying nav link.\n   */\n  tabAttrs: PropTypes.object\n};\nconst Tab = () => {\n  throw new Error('ReactBootstrap: The `Tab` component is not meant to be rendered! ' + \"It's an abstract component that is only valid as a direct Child of the `Tabs` Component. \" + 'For custom tabs components use TabPane and TabsContainer directly');\n};\nTab.propTypes = propTypes;\nexport default Object.assign(Tab, {\n  Container: TabContainer,\n  Content: TabContent,\n  Pane: TabPane\n});"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,OAAO,MAAM,WAAW;AAC/B,MAAMC,SAAS,GAAG;EAChBC,QAAQ,EAAEL,SAAS,CAACM,SAAS,CAAC,CAACN,SAAS,CAACO,MAAM,EAAEP,SAAS,CAACQ,MAAM,CAAC,CAAC;EACnE;AACF;AACA;EACEC,KAAK,EAAET,SAAS,CAACU,IAAI,CAACC,UAAU;EAChC;AACF;AACA;EACEC,QAAQ,EAAEZ,SAAS,CAACa,IAAI;EACxB;AACF;AACA;EACEC,YAAY,EAAEd,SAAS,CAACO,MAAM;EAC9B;AACF;AACA;EACEQ,QAAQ,EAAEf,SAAS,CAACgB;AACtB,CAAC;AACD,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAChB,MAAM,IAAIC,KAAK,CAAC,mEAAmE,GAAG,2FAA2F,GAAG,mEAAmE,CAAC;AAC1P,CAAC;AACDD,GAAG,CAACb,SAAS,GAAGA,SAAS;AACzB,eAAee,MAAM,CAACC,MAAM,CAACH,GAAG,EAAE;EAChCI,SAAS,EAAEpB,YAAY;EACvBqB,OAAO,EAAEpB,UAAU;EACnBqB,IAAI,EAAEpB;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}