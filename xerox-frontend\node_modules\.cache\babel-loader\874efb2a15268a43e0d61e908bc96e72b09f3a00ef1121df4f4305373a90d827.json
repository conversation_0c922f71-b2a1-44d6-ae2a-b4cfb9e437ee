{"ast": null, "code": "import { useRef } from 'react';\n\n/**\n * Returns a ref that is immediately updated with the new value\n *\n * @param value The Ref value\n * @category refs\n */\nexport default function useUpdatedRef(value) {\n  const valueRef = useRef(value);\n  valueRef.current = value;\n  return valueRef;\n}", "map": {"version": 3, "names": ["useRef", "useUpdatedRef", "value", "valueRef", "current"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@restart/hooks/esm/useUpdatedRef.js"], "sourcesContent": ["import { useRef } from 'react';\n\n/**\n * Returns a ref that is immediately updated with the new value\n *\n * @param value The Ref value\n * @category refs\n */\nexport default function useUpdatedRef(value) {\n  const valueRef = useRef(value);\n  valueRef.current = value;\n  return valueRef;\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC3C,MAAMC,QAAQ,GAAGH,MAAM,CAACE,KAAK,CAAC;EAC9BC,QAAQ,CAACC,OAAO,GAAGF,KAAK;EACxB,OAAOC,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}