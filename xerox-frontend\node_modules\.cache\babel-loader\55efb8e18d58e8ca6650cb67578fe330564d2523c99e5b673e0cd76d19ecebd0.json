{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport CloseButton from './CloseButton';\nimport ModalContext from './ModalContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst AbstractModalHeader = /*#__PURE__*/React.forwardRef(({\n  closeLabel = 'Close',\n  closeVariant,\n  closeButton = false,\n  onHide,\n  children,\n  ...props\n}, ref) => {\n  const context = useContext(ModalContext);\n  const handleClick = useEventCallback(() => {\n    context == null || context.onHide();\n    onHide == null || onHide();\n  });\n  return /*#__PURE__*/_jsxs(\"div\", {\n    ref: ref,\n    ...props,\n    children: [children, closeButton && /*#__PURE__*/_jsx(CloseButton, {\n      \"aria-label\": closeLabel,\n      variant: closeVariant,\n      onClick: handleClick\n    })]\n  });\n});\nAbstractModalHeader.displayName = 'AbstractModalHeader';\nexport default AbstractModalHeader;", "map": {"version": 3, "names": ["React", "useContext", "useEventCallback", "CloseButton", "ModalContext", "jsx", "_jsx", "jsxs", "_jsxs", "AbstractModalHeader", "forwardRef", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "closeButton", "onHide", "children", "props", "ref", "context", "handleClick", "variant", "onClick", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/AbstractModalHeader.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport CloseButton from './CloseButton';\nimport ModalContext from './ModalContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst AbstractModalHeader = /*#__PURE__*/React.forwardRef(({\n  closeLabel = 'Close',\n  closeVariant,\n  closeButton = false,\n  onHide,\n  children,\n  ...props\n}, ref) => {\n  const context = useContext(ModalContext);\n  const handleClick = useEventCallback(() => {\n    context == null || context.onHide();\n    onHide == null || onHide();\n  });\n  return /*#__PURE__*/_jsxs(\"div\", {\n    ref: ref,\n    ...props,\n    children: [children, closeButton && /*#__PURE__*/_jsx(CloseButton, {\n      \"aria-label\": closeLabel,\n      variant: closeVariant,\n      onClick: handleClick\n    })]\n  });\n});\nAbstractModalHeader.displayName = 'AbstractModalHeader';\nexport default AbstractModalHeader;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,mBAAmB,GAAG,aAAaT,KAAK,CAACU,UAAU,CAAC,CAAC;EACzDC,UAAU,GAAG,OAAO;EACpBC,YAAY;EACZC,WAAW,GAAG,KAAK;EACnBC,MAAM;EACNC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,OAAO,GAAGjB,UAAU,CAACG,YAAY,CAAC;EACxC,MAAMe,WAAW,GAAGjB,gBAAgB,CAAC,MAAM;IACzCgB,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACJ,MAAM,CAAC,CAAC;IACnCA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC,CAAC;EAC5B,CAAC,CAAC;EACF,OAAO,aAAaN,KAAK,CAAC,KAAK,EAAE;IAC/BS,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRD,QAAQ,EAAE,CAACA,QAAQ,EAAEF,WAAW,IAAI,aAAaP,IAAI,CAACH,WAAW,EAAE;MACjE,YAAY,EAAEQ,UAAU;MACxBS,OAAO,EAAER,YAAY;MACrBS,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFV,mBAAmB,CAACa,WAAW,GAAG,qBAAqB;AACvD,eAAeb,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}