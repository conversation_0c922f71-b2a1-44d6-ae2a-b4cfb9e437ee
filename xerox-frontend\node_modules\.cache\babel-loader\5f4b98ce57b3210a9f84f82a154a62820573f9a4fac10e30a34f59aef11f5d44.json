{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport classNames from 'classnames';\nimport BaseOverlay from '@restart/ui/Overlay';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useIsomorphicEffect from '@restart/hooks/useIsomorphicEffect';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport useOverlayOffset from './useOverlayOffset';\nimport Fade from './Fade';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction wrapRefs(props, arrowProps) {\n  const {\n    ref\n  } = props;\n  const {\n    ref: aRef\n  } = arrowProps;\n  props.ref = ref.__wrapped || (ref.__wrapped = r => ref(safeFindDOMNode(r)));\n  arrowProps.ref = aRef.__wrapped || (aRef.__wrapped = r => aRef(safeFindDOMNode(r)));\n}\nconst Overlay = /*#__PURE__*/React.forwardRef(({\n  children: overlay,\n  transition = Fade,\n  popperConfig = {},\n  rootClose = false,\n  placement = 'top',\n  show: outerShow = false,\n  ...outerProps\n}, outerRef) => {\n  const popperRef = useRef({});\n  const [firstRenderedState, setFirstRenderedState] = useState(null);\n  const [ref, modifiers] = useOverlayOffset(outerProps.offset);\n  const mergedRef = useMergedRefs(outerRef, ref);\n  const actualTransition = transition === true ? Fade : transition || undefined;\n  const handleFirstUpdate = useEventCallback(state => {\n    setFirstRenderedState(state);\n    popperConfig == null || popperConfig.onFirstUpdate == null || popperConfig.onFirstUpdate(state);\n  });\n  useIsomorphicEffect(() => {\n    if (firstRenderedState && outerProps.target) {\n      // Must wait for target element to resolve before updating popper.\n      popperRef.current.scheduleUpdate == null || popperRef.current.scheduleUpdate();\n    }\n  }, [firstRenderedState, outerProps.target]);\n  useEffect(() => {\n    if (!outerShow) {\n      setFirstRenderedState(null);\n    }\n  }, [outerShow]);\n  return /*#__PURE__*/_jsx(BaseOverlay, {\n    ...outerProps,\n    ref: mergedRef,\n    popperConfig: {\n      ...popperConfig,\n      modifiers: modifiers.concat(popperConfig.modifiers || []),\n      onFirstUpdate: handleFirstUpdate\n    },\n    transition: actualTransition,\n    rootClose: rootClose,\n    placement: placement,\n    show: outerShow,\n    children: (overlayProps, {\n      arrowProps,\n      popper: popperObj,\n      show\n    }) => {\n      var _popperObj$state;\n      wrapRefs(overlayProps, arrowProps);\n      // Need to get placement from popper object, handling case when overlay is flipped using 'flip' prop\n      const updatedPlacement = popperObj == null ? void 0 : popperObj.placement;\n      const popper = Object.assign(popperRef.current, {\n        state: popperObj == null ? void 0 : popperObj.state,\n        scheduleUpdate: popperObj == null ? void 0 : popperObj.update,\n        placement: updatedPlacement,\n        outOfBoundaries: (popperObj == null || (_popperObj$state = popperObj.state) == null || (_popperObj$state = _popperObj$state.modifiersData.hide) == null ? void 0 : _popperObj$state.isReferenceHidden) || false,\n        strategy: popperConfig.strategy\n      });\n      const hasDoneInitialMeasure = !!firstRenderedState;\n      if (typeof overlay === 'function') return overlay({\n        ...overlayProps,\n        placement: updatedPlacement,\n        show,\n        ...(!transition && show && {\n          className: 'show'\n        }),\n        popper,\n        arrowProps,\n        hasDoneInitialMeasure\n      });\n      return /*#__PURE__*/React.cloneElement(overlay, {\n        ...overlayProps,\n        placement: updatedPlacement,\n        arrowProps,\n        popper,\n        hasDoneInitialMeasure,\n        className: classNames(overlay.props.className, !transition && show && 'show'),\n        style: {\n          ...overlay.props.style,\n          ...overlayProps.style\n        }\n      });\n    }\n  });\n});\nOverlay.displayName = 'Overlay';\nexport default Overlay;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "classNames", "BaseOverlay", "useEventCallback", "useIsomorphicEffect", "useMergedRefs", "useOverlayOffset", "Fade", "safeFindDOMNode", "jsx", "_jsx", "wrapRefs", "props", "arrowProps", "ref", "aRef", "__wrapped", "r", "Overlay", "forwardRef", "children", "overlay", "transition", "popperConfig", "rootClose", "placement", "show", "outerShow", "outerProps", "outerRef", "popperRef", "firstRenderedState", "setFirstRenderedState", "modifiers", "offset", "mergedRef", "actualTransition", "undefined", "handleFirstUpdate", "state", "onFirstUpdate", "target", "current", "scheduleUpdate", "concat", "overlayProps", "popper", "popperObj", "_popperObj$state", "updatedPlacement", "Object", "assign", "update", "outOfBoundaries", "modifiersData", "hide", "isReferenceHidden", "strategy", "hasDoneInitialMeasure", "className", "cloneElement", "style", "displayName"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/Overlay.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport classNames from 'classnames';\nimport BaseOverlay from '@restart/ui/Overlay';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport useIsomorphicEffect from '@restart/hooks/useIsomorphicEffect';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport useOverlayOffset from './useOverlayOffset';\nimport Fade from './Fade';\nimport safeFindDOMNode from './safeFindDOMNode';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction wrapRefs(props, arrowProps) {\n  const {\n    ref\n  } = props;\n  const {\n    ref: aRef\n  } = arrowProps;\n  props.ref = ref.__wrapped || (ref.__wrapped = r => ref(safeFindDOMNode(r)));\n  arrowProps.ref = aRef.__wrapped || (aRef.__wrapped = r => aRef(safeFindDOMNode(r)));\n}\nconst Overlay = /*#__PURE__*/React.forwardRef(({\n  children: overlay,\n  transition = Fade,\n  popperConfig = {},\n  rootClose = false,\n  placement = 'top',\n  show: outerShow = false,\n  ...outerProps\n}, outerRef) => {\n  const popperRef = useRef({});\n  const [firstRenderedState, setFirstRenderedState] = useState(null);\n  const [ref, modifiers] = useOverlayOffset(outerProps.offset);\n  const mergedRef = useMergedRefs(outerRef, ref);\n  const actualTransition = transition === true ? Fade : transition || undefined;\n  const handleFirstUpdate = useEventCallback(state => {\n    setFirstRenderedState(state);\n    popperConfig == null || popperConfig.onFirstUpdate == null || popperConfig.onFirstUpdate(state);\n  });\n  useIsomorphicEffect(() => {\n    if (firstRenderedState && outerProps.target) {\n      // Must wait for target element to resolve before updating popper.\n      popperRef.current.scheduleUpdate == null || popperRef.current.scheduleUpdate();\n    }\n  }, [firstRenderedState, outerProps.target]);\n  useEffect(() => {\n    if (!outerShow) {\n      setFirstRenderedState(null);\n    }\n  }, [outerShow]);\n  return /*#__PURE__*/_jsx(BaseOverlay, {\n    ...outerProps,\n    ref: mergedRef,\n    popperConfig: {\n      ...popperConfig,\n      modifiers: modifiers.concat(popperConfig.modifiers || []),\n      onFirstUpdate: handleFirstUpdate\n    },\n    transition: actualTransition,\n    rootClose: rootClose,\n    placement: placement,\n    show: outerShow,\n    children: (overlayProps, {\n      arrowProps,\n      popper: popperObj,\n      show\n    }) => {\n      var _popperObj$state;\n      wrapRefs(overlayProps, arrowProps);\n      // Need to get placement from popper object, handling case when overlay is flipped using 'flip' prop\n      const updatedPlacement = popperObj == null ? void 0 : popperObj.placement;\n      const popper = Object.assign(popperRef.current, {\n        state: popperObj == null ? void 0 : popperObj.state,\n        scheduleUpdate: popperObj == null ? void 0 : popperObj.update,\n        placement: updatedPlacement,\n        outOfBoundaries: (popperObj == null || (_popperObj$state = popperObj.state) == null || (_popperObj$state = _popperObj$state.modifiersData.hide) == null ? void 0 : _popperObj$state.isReferenceHidden) || false,\n        strategy: popperConfig.strategy\n      });\n      const hasDoneInitialMeasure = !!firstRenderedState;\n      if (typeof overlay === 'function') return overlay({\n        ...overlayProps,\n        placement: updatedPlacement,\n        show,\n        ...(!transition && show && {\n          className: 'show'\n        }),\n        popper,\n        arrowProps,\n        hasDoneInitialMeasure\n      });\n      return /*#__PURE__*/React.cloneElement(overlay, {\n        ...overlayProps,\n        placement: updatedPlacement,\n        arrowProps,\n        popper,\n        hasDoneInitialMeasure,\n        className: classNames(overlay.props.className, !transition && show && 'show'),\n        style: {\n          ...overlay.props.style,\n          ...overlayProps.style\n        }\n      });\n    }\n  });\n});\nOverlay.displayName = 'Overlay';\nexport default Overlay;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,aAAa,MAAM,8BAA8B;AACxD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQA,CAACC,KAAK,EAAEC,UAAU,EAAE;EACnC,MAAM;IACJC;EACF,CAAC,GAAGF,KAAK;EACT,MAAM;IACJE,GAAG,EAAEC;EACP,CAAC,GAAGF,UAAU;EACdD,KAAK,CAACE,GAAG,GAAGA,GAAG,CAACE,SAAS,KAAKF,GAAG,CAACE,SAAS,GAAGC,CAAC,IAAIH,GAAG,CAACN,eAAe,CAACS,CAAC,CAAC,CAAC,CAAC;EAC3EJ,UAAU,CAACC,GAAG,GAAGC,IAAI,CAACC,SAAS,KAAKD,IAAI,CAACC,SAAS,GAAGC,CAAC,IAAIF,IAAI,CAACP,eAAe,CAACS,CAAC,CAAC,CAAC,CAAC;AACrF;AACA,MAAMC,OAAO,GAAG,aAAarB,KAAK,CAACsB,UAAU,CAAC,CAAC;EAC7CC,QAAQ,EAAEC,OAAO;EACjBC,UAAU,GAAGf,IAAI;EACjBgB,YAAY,GAAG,CAAC,CAAC;EACjBC,SAAS,GAAG,KAAK;EACjBC,SAAS,GAAG,KAAK;EACjBC,IAAI,EAAEC,SAAS,GAAG,KAAK;EACvB,GAAGC;AACL,CAAC,EAAEC,QAAQ,KAAK;EACd,MAAMC,SAAS,GAAG/B,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5B,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACc,GAAG,EAAEmB,SAAS,CAAC,GAAG3B,gBAAgB,CAACsB,UAAU,CAACM,MAAM,CAAC;EAC5D,MAAMC,SAAS,GAAG9B,aAAa,CAACwB,QAAQ,EAAEf,GAAG,CAAC;EAC9C,MAAMsB,gBAAgB,GAAGd,UAAU,KAAK,IAAI,GAAGf,IAAI,GAAGe,UAAU,IAAIe,SAAS;EAC7E,MAAMC,iBAAiB,GAAGnC,gBAAgB,CAACoC,KAAK,IAAI;IAClDP,qBAAqB,CAACO,KAAK,CAAC;IAC5BhB,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACiB,aAAa,IAAI,IAAI,IAAIjB,YAAY,CAACiB,aAAa,CAACD,KAAK,CAAC;EACjG,CAAC,CAAC;EACFnC,mBAAmB,CAAC,MAAM;IACxB,IAAI2B,kBAAkB,IAAIH,UAAU,CAACa,MAAM,EAAE;MAC3C;MACAX,SAAS,CAACY,OAAO,CAACC,cAAc,IAAI,IAAI,IAAIb,SAAS,CAACY,OAAO,CAACC,cAAc,CAAC,CAAC;IAChF;EACF,CAAC,EAAE,CAACZ,kBAAkB,EAAEH,UAAU,CAACa,MAAM,CAAC,CAAC;EAC3C3C,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6B,SAAS,EAAE;MACdK,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC,EAAE,CAACL,SAAS,CAAC,CAAC;EACf,OAAO,aAAajB,IAAI,CAACR,WAAW,EAAE;IACpC,GAAG0B,UAAU;IACbd,GAAG,EAAEqB,SAAS;IACdZ,YAAY,EAAE;MACZ,GAAGA,YAAY;MACfU,SAAS,EAAEA,SAAS,CAACW,MAAM,CAACrB,YAAY,CAACU,SAAS,IAAI,EAAE,CAAC;MACzDO,aAAa,EAAEF;IACjB,CAAC;IACDhB,UAAU,EAAEc,gBAAgB;IAC5BZ,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEA,SAAS;IACpBC,IAAI,EAAEC,SAAS;IACfP,QAAQ,EAAEA,CAACyB,YAAY,EAAE;MACvBhC,UAAU;MACViC,MAAM,EAAEC,SAAS;MACjBrB;IACF,CAAC,KAAK;MACJ,IAAIsB,gBAAgB;MACpBrC,QAAQ,CAACkC,YAAY,EAAEhC,UAAU,CAAC;MAClC;MACA,MAAMoC,gBAAgB,GAAGF,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACtB,SAAS;MACzE,MAAMqB,MAAM,GAAGI,MAAM,CAACC,MAAM,CAACrB,SAAS,CAACY,OAAO,EAAE;QAC9CH,KAAK,EAAEQ,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACR,KAAK;QACnDI,cAAc,EAAEI,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACK,MAAM;QAC7D3B,SAAS,EAAEwB,gBAAgB;QAC3BI,eAAe,EAAE,CAACN,SAAS,IAAI,IAAI,IAAI,CAACC,gBAAgB,GAAGD,SAAS,CAACR,KAAK,KAAK,IAAI,IAAI,CAACS,gBAAgB,GAAGA,gBAAgB,CAACM,aAAa,CAACC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGP,gBAAgB,CAACQ,iBAAiB,KAAK,KAAK;QAC/MC,QAAQ,EAAElC,YAAY,CAACkC;MACzB,CAAC,CAAC;MACF,MAAMC,qBAAqB,GAAG,CAAC,CAAC3B,kBAAkB;MAClD,IAAI,OAAOV,OAAO,KAAK,UAAU,EAAE,OAAOA,OAAO,CAAC;QAChD,GAAGwB,YAAY;QACfpB,SAAS,EAAEwB,gBAAgB;QAC3BvB,IAAI;QACJ,IAAI,CAACJ,UAAU,IAAII,IAAI,IAAI;UACzBiC,SAAS,EAAE;QACb,CAAC,CAAC;QACFb,MAAM;QACNjC,UAAU;QACV6C;MACF,CAAC,CAAC;MACF,OAAO,aAAa7D,KAAK,CAAC+D,YAAY,CAACvC,OAAO,EAAE;QAC9C,GAAGwB,YAAY;QACfpB,SAAS,EAAEwB,gBAAgB;QAC3BpC,UAAU;QACViC,MAAM;QACNY,qBAAqB;QACrBC,SAAS,EAAE1D,UAAU,CAACoB,OAAO,CAACT,KAAK,CAAC+C,SAAS,EAAE,CAACrC,UAAU,IAAII,IAAI,IAAI,MAAM,CAAC;QAC7EmC,KAAK,EAAE;UACL,GAAGxC,OAAO,CAACT,KAAK,CAACiD,KAAK;UACtB,GAAGhB,YAAY,CAACgB;QAClB;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;AACF3C,OAAO,CAAC4C,WAAW,GAAG,SAAS;AAC/B,eAAe5C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}