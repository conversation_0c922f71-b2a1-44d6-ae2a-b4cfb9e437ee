﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace XeroxModule.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ZRX_User",
                columns: table => new
                {
                    UserID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Username = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    PasswordHash = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    UserType = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastLoginAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ZRX_User", x => x.UserID);
                });

            migrationBuilder.CreateTable(
                name: "STU_Student",
                columns: table => new
                {
                    StudentID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserID = table.Column<int>(type: "int", nullable: false),
                    StudentNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    FirstName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    LastName = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    PhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Department = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Year = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ModifiedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_STU_Student", x => x.StudentID);
                    table.ForeignKey(
                        name: "FK_STU_Student_ZRX_User_UserID",
                        column: x => x.UserID,
                        principalTable: "ZRX_User",
                        principalColumn: "UserID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ZRX_Notification",
                columns: table => new
                {
                    NotificationID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserID = table.Column<int>(type: "int", nullable: false),
                    Title = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Message = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    RelatedEntityID = table.Column<int>(type: "int", nullable: true),
                    RelatedEntityType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    IsRead = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ZRX_Notification", x => x.NotificationID);
                    table.ForeignKey(
                        name: "FK_ZRX_Notification_ZRX_User_UserID",
                        column: x => x.UserID,
                        principalTable: "ZRX_User",
                        principalColumn: "UserID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ZRX_XeroxCenter",
                columns: table => new
                {
                    XeroxCenterID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserID = table.Column<int>(type: "int", nullable: false),
                    XeroxCenterName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Location = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ContactPerson = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    PhoneNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    TotalJobs = table.Column<int>(type: "int", nullable: false),
                    PendingJobs = table.Column<int>(type: "int", nullable: false),
                    CompletedJobs = table.Column<int>(type: "int", nullable: false),
                    AverageRating = table.Column<decimal>(type: "decimal(3,2)", nullable: true),
                    TotalRatings = table.Column<int>(type: "int", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedUserID = table.Column<int>(type: "int", nullable: false),
                    ModifiedUserID = table.Column<int>(type: "int", nullable: true),
                    Created = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Modified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ZRX_XeroxCenter", x => x.XeroxCenterID);
                    table.ForeignKey(
                        name: "FK_ZRX_XeroxCenter_ZRX_User_CreatedUserID",
                        column: x => x.CreatedUserID,
                        principalTable: "ZRX_User",
                        principalColumn: "UserID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ZRX_XeroxCenter_ZRX_User_ModifiedUserID",
                        column: x => x.ModifiedUserID,
                        principalTable: "ZRX_User",
                        principalColumn: "UserID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ZRX_XeroxCenter_ZRX_User_UserID",
                        column: x => x.UserID,
                        principalTable: "ZRX_User",
                        principalColumn: "UserID",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ZRX_FileUpload",
                columns: table => new
                {
                    FileUploadID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FileName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    OriginalFileName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    FilePath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    FileType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Remarks = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    StudentID = table.Column<int>(type: "int", nullable: false),
                    PreferredXeroxCenterID = table.Column<int>(type: "int", nullable: true),
                    UploadedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    PrintType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Copies = table.Column<int>(type: "int", nullable: false),
                    ColorType = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    PaperSize = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    CreatedUserID = table.Column<int>(type: "int", nullable: false),
                    ModifiedUserID = table.Column<int>(type: "int", nullable: true),
                    Created = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Modified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ZRX_FileUpload", x => x.FileUploadID);
                    table.ForeignKey(
                        name: "FK_ZRX_FileUpload_STU_Student_StudentID",
                        column: x => x.StudentID,
                        principalTable: "STU_Student",
                        principalColumn: "StudentID",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ZRX_FileUpload_ZRX_User_CreatedUserID",
                        column: x => x.CreatedUserID,
                        principalTable: "ZRX_User",
                        principalColumn: "UserID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ZRX_FileUpload_ZRX_User_ModifiedUserID",
                        column: x => x.ModifiedUserID,
                        principalTable: "ZRX_User",
                        principalColumn: "UserID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ZRX_FileUpload_ZRX_XeroxCenter_PreferredXeroxCenterID",
                        column: x => x.PreferredXeroxCenterID,
                        principalTable: "ZRX_XeroxCenter",
                        principalColumn: "XeroxCenterID",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ZRX_PrintJob",
                columns: table => new
                {
                    PrintJobID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    JobNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    XeroxCenterID = table.Column<int>(type: "int", nullable: false),
                    FileUploadID = table.Column<int>(type: "int", nullable: false),
                    Cost = table.Column<decimal>(type: "decimal(10,2)", nullable: true),
                    Status = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    EstimatedCompletionTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ActualCompletionTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeliveredAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Priority = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    CreatedUserID = table.Column<int>(type: "int", nullable: false),
                    ModifiedUserID = table.Column<int>(type: "int", nullable: true),
                    Created = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Modified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ZRX_PrintJob", x => x.PrintJobID);
                    table.ForeignKey(
                        name: "FK_ZRX_PrintJob_ZRX_FileUpload_FileUploadID",
                        column: x => x.FileUploadID,
                        principalTable: "ZRX_FileUpload",
                        principalColumn: "FileUploadID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ZRX_PrintJob_ZRX_User_CreatedUserID",
                        column: x => x.CreatedUserID,
                        principalTable: "ZRX_User",
                        principalColumn: "UserID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ZRX_PrintJob_ZRX_User_ModifiedUserID",
                        column: x => x.ModifiedUserID,
                        principalTable: "ZRX_User",
                        principalColumn: "UserID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ZRX_PrintJob_ZRX_XeroxCenter_XeroxCenterID",
                        column: x => x.XeroxCenterID,
                        principalTable: "ZRX_XeroxCenter",
                        principalColumn: "XeroxCenterID",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ZRX_Message",
                columns: table => new
                {
                    MessageID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PrintJobID = table.Column<int>(type: "int", nullable: false),
                    SenderUserID = table.Column<int>(type: "int", nullable: false),
                    ReceiverUserID = table.Column<int>(type: "int", nullable: false),
                    MessageText = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: false),
                    IsRead = table.Column<bool>(type: "bit", nullable: false),
                    SentAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ZRX_Message", x => x.MessageID);
                    table.ForeignKey(
                        name: "FK_ZRX_Message_ZRX_PrintJob_PrintJobID",
                        column: x => x.PrintJobID,
                        principalTable: "ZRX_PrintJob",
                        principalColumn: "PrintJobID",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ZRX_Message_ZRX_User_ReceiverUserID",
                        column: x => x.ReceiverUserID,
                        principalTable: "ZRX_User",
                        principalColumn: "UserID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ZRX_Message_ZRX_User_SenderUserID",
                        column: x => x.SenderUserID,
                        principalTable: "ZRX_User",
                        principalColumn: "UserID",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ZRX_Rating",
                columns: table => new
                {
                    RatingID = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PrintJobID = table.Column<int>(type: "int", nullable: false),
                    StudentID = table.Column<int>(type: "int", nullable: false),
                    XeroxCenterID = table.Column<int>(type: "int", nullable: false),
                    RatingValue = table.Column<int>(type: "int", nullable: false),
                    Review = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ZRX_Rating", x => x.RatingID);
                    table.ForeignKey(
                        name: "FK_ZRX_Rating_STU_Student_StudentID",
                        column: x => x.StudentID,
                        principalTable: "STU_Student",
                        principalColumn: "StudentID",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ZRX_Rating_ZRX_PrintJob_PrintJobID",
                        column: x => x.PrintJobID,
                        principalTable: "ZRX_PrintJob",
                        principalColumn: "PrintJobID",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ZRX_Rating_ZRX_XeroxCenter_XeroxCenterID",
                        column: x => x.XeroxCenterID,
                        principalTable: "ZRX_XeroxCenter",
                        principalColumn: "XeroxCenterID",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_STU_Student_StudentNumber",
                table: "STU_Student",
                column: "StudentNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_STU_Student_UserID",
                table: "STU_Student",
                column: "UserID",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_FileUpload_CreatedUserID",
                table: "ZRX_FileUpload",
                column: "CreatedUserID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_FileUpload_ModifiedUserID",
                table: "ZRX_FileUpload",
                column: "ModifiedUserID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_FileUpload_PreferredXeroxCenterID",
                table: "ZRX_FileUpload",
                column: "PreferredXeroxCenterID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_FileUpload_StudentID",
                table: "ZRX_FileUpload",
                column: "StudentID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_Message_PrintJobID",
                table: "ZRX_Message",
                column: "PrintJobID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_Message_ReceiverUserID",
                table: "ZRX_Message",
                column: "ReceiverUserID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_Message_SenderUserID",
                table: "ZRX_Message",
                column: "SenderUserID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_Notification_UserID",
                table: "ZRX_Notification",
                column: "UserID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_PrintJob_CreatedUserID",
                table: "ZRX_PrintJob",
                column: "CreatedUserID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_PrintJob_FileUploadID",
                table: "ZRX_PrintJob",
                column: "FileUploadID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_PrintJob_JobNumber",
                table: "ZRX_PrintJob",
                column: "JobNumber",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_PrintJob_ModifiedUserID",
                table: "ZRX_PrintJob",
                column: "ModifiedUserID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_PrintJob_XeroxCenterID",
                table: "ZRX_PrintJob",
                column: "XeroxCenterID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_Rating_PrintJobID_StudentID",
                table: "ZRX_Rating",
                columns: new[] { "PrintJobID", "StudentID" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_Rating_StudentID",
                table: "ZRX_Rating",
                column: "StudentID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_Rating_XeroxCenterID",
                table: "ZRX_Rating",
                column: "XeroxCenterID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_User_Email",
                table: "ZRX_User",
                column: "Email",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_User_Username",
                table: "ZRX_User",
                column: "Username",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_XeroxCenter_CreatedUserID",
                table: "ZRX_XeroxCenter",
                column: "CreatedUserID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_XeroxCenter_ModifiedUserID",
                table: "ZRX_XeroxCenter",
                column: "ModifiedUserID");

            migrationBuilder.CreateIndex(
                name: "IX_ZRX_XeroxCenter_UserID",
                table: "ZRX_XeroxCenter",
                column: "UserID",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ZRX_Message");

            migrationBuilder.DropTable(
                name: "ZRX_Notification");

            migrationBuilder.DropTable(
                name: "ZRX_Rating");

            migrationBuilder.DropTable(
                name: "ZRX_PrintJob");

            migrationBuilder.DropTable(
                name: "ZRX_FileUpload");

            migrationBuilder.DropTable(
                name: "STU_Student");

            migrationBuilder.DropTable(
                name: "ZRX_XeroxCenter");

            migrationBuilder.DropTable(
                name: "ZRX_User");
        }
    }
}
