{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport BaseNav from '@restart/ui/Nav';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport CardHeaderContext from './CardHeaderContext';\nimport NavItem from './NavItem';\nimport NavLink from './NavLink';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Nav = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    as = 'div',\n    bsPrefix: initialBsPrefix,\n    variant,\n    fill = false,\n    justify = false,\n    navbar,\n    navbarScroll,\n    className,\n    activeKey,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    activeKey: 'onSelect'\n  });\n  const bsPrefix = useBootstrapPrefix(initialBsPrefix, 'nav');\n  let navbarBsPrefix;\n  let cardHeaderBsPrefix;\n  let isNavbar = false;\n  const navbarContext = useContext(NavbarContext);\n  const cardHeaderContext = useContext(CardHeaderContext);\n  if (navbarContext) {\n    navbarBsPrefix = navbarContext.bsPrefix;\n    isNavbar = navbar == null ? true : navbar;\n  } else if (cardHeaderContext) {\n    ({\n      cardHeaderBsPrefix\n    } = cardHeaderContext);\n  }\n  return /*#__PURE__*/_jsx(BaseNav, {\n    as: as,\n    ref: ref,\n    activeKey: activeKey,\n    className: classNames(className, {\n      [bsPrefix]: !isNavbar,\n      [`${navbarBsPrefix}-nav`]: isNavbar,\n      [`${navbarBsPrefix}-nav-scroll`]: isNavbar && navbarScroll,\n      [`${cardHeaderBsPrefix}-${variant}`]: !!cardHeaderBsPrefix,\n      [`${bsPrefix}-${variant}`]: !!variant,\n      [`${bsPrefix}-fill`]: fill,\n      [`${bsPrefix}-justified`]: justify\n    }),\n    ...props\n  });\n});\nNav.displayName = 'Nav';\nexport default Object.assign(Nav, {\n  Item: NavItem,\n  Link: NavLink\n});", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useUncontrolled", "BaseNav", "useBootstrapPrefix", "NavbarContext", "CardHeaderContext", "NavItem", "NavLink", "jsx", "_jsx", "Nav", "forwardRef", "uncontrolledProps", "ref", "as", "bsPrefix", "initialBsPrefix", "variant", "fill", "justify", "navbar", "navbarScroll", "className", "active<PERSON><PERSON>", "props", "navbarBsPrefix", "cardHeaderBsPrefix", "isNavbar", "navbarContext", "cardHeaderContext", "displayName", "Object", "assign", "<PERSON><PERSON>", "Link"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/react-bootstrap/esm/Nav.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport BaseNav from '@restart/ui/Nav';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport CardHeaderContext from './CardHeaderContext';\nimport NavItem from './NavItem';\nimport NavLink from './NavLink';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Nav = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    as = 'div',\n    bsPrefix: initialBsPrefix,\n    variant,\n    fill = false,\n    justify = false,\n    navbar,\n    navbarScroll,\n    className,\n    activeKey,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    activeKey: 'onSelect'\n  });\n  const bsPrefix = useBootstrapPrefix(initialBsPrefix, 'nav');\n  let navbarBsPrefix;\n  let cardHeaderBsPrefix;\n  let isNavbar = false;\n  const navbarContext = useContext(NavbarContext);\n  const cardHeaderContext = useContext(CardHeaderContext);\n  if (navbarContext) {\n    navbarBsPrefix = navbarContext.bsPrefix;\n    isNavbar = navbar == null ? true : navbar;\n  } else if (cardHeaderContext) {\n    ({\n      cardHeaderBsPrefix\n    } = cardHeaderContext);\n  }\n  return /*#__PURE__*/_jsx(BaseNav, {\n    as: as,\n    ref: ref,\n    activeKey: activeKey,\n    className: classNames(className, {\n      [bsPrefix]: !isNavbar,\n      [`${navbarBsPrefix}-nav`]: isNavbar,\n      [`${navbarBsPrefix}-nav-scroll`]: isNavbar && navbarScroll,\n      [`${cardHeaderBsPrefix}-${variant}`]: !!cardHeaderBsPrefix,\n      [`${bsPrefix}-${variant}`]: !!variant,\n      [`${bsPrefix}-fill`]: fill,\n      [`${bsPrefix}-justified`]: justify\n    }),\n    ...props\n  });\n});\nNav.displayName = 'Nav';\nexport default Object.assign(Nav, {\n  Item: NavItem,\n  Link: NavLink\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,GAAG,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,CAACC,iBAAiB,EAAEC,GAAG,KAAK;EACpE,MAAM;IACJC,EAAE,GAAG,KAAK;IACVC,QAAQ,EAAEC,eAAe;IACzBC,OAAO;IACPC,IAAI,GAAG,KAAK;IACZC,OAAO,GAAG,KAAK;IACfC,MAAM;IACNC,YAAY;IACZC,SAAS;IACTC,SAAS;IACT,GAAGC;EACL,CAAC,GAAGvB,eAAe,CAACW,iBAAiB,EAAE;IACrCW,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMR,QAAQ,GAAGZ,kBAAkB,CAACa,eAAe,EAAE,KAAK,CAAC;EAC3D,IAAIS,cAAc;EAClB,IAAIC,kBAAkB;EACtB,IAAIC,QAAQ,GAAG,KAAK;EACpB,MAAMC,aAAa,GAAG5B,UAAU,CAACI,aAAa,CAAC;EAC/C,MAAMyB,iBAAiB,GAAG7B,UAAU,CAACK,iBAAiB,CAAC;EACvD,IAAIuB,aAAa,EAAE;IACjBH,cAAc,GAAGG,aAAa,CAACb,QAAQ;IACvCY,QAAQ,GAAGP,MAAM,IAAI,IAAI,GAAG,IAAI,GAAGA,MAAM;EAC3C,CAAC,MAAM,IAAIS,iBAAiB,EAAE;IAC5B,CAAC;MACCH;IACF,CAAC,GAAGG,iBAAiB;EACvB;EACA,OAAO,aAAapB,IAAI,CAACP,OAAO,EAAE;IAChCY,EAAE,EAAEA,EAAE;IACND,GAAG,EAAEA,GAAG;IACRU,SAAS,EAAEA,SAAS;IACpBD,SAAS,EAAExB,UAAU,CAACwB,SAAS,EAAE;MAC/B,CAACP,QAAQ,GAAG,CAACY,QAAQ;MACrB,CAAC,GAAGF,cAAc,MAAM,GAAGE,QAAQ;MACnC,CAAC,GAAGF,cAAc,aAAa,GAAGE,QAAQ,IAAIN,YAAY;MAC1D,CAAC,GAAGK,kBAAkB,IAAIT,OAAO,EAAE,GAAG,CAAC,CAACS,kBAAkB;MAC1D,CAAC,GAAGX,QAAQ,IAAIE,OAAO,EAAE,GAAG,CAAC,CAACA,OAAO;MACrC,CAAC,GAAGF,QAAQ,OAAO,GAAGG,IAAI;MAC1B,CAAC,GAAGH,QAAQ,YAAY,GAAGI;IAC7B,CAAC,CAAC;IACF,GAAGK;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFd,GAAG,CAACoB,WAAW,GAAG,KAAK;AACvB,eAAeC,MAAM,CAACC,MAAM,CAACtB,GAAG,EAAE;EAChCuB,IAAI,EAAE3B,OAAO;EACb4B,IAAI,EAAE3B;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}