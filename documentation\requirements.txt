Printing or Stationary (Zerox) Module


- there will be two different type of user, student and zerox center.
- student will upload their contect as a pdf, word, zip. with his/her preferred center if any and remarks if any.
- zerox center can view the content and give response with cost and estimated time to complete the task.
- response will be sent to student for confirmation regarding cost and time.
- further communication can be done through that window screen (kind of ticket response system)
- after final confirmation from student, zerox center will start their task to print or zerox the content.
- various tags will be there like requested, waiting for confirmation, confirmed, completed, rejected etc. (you can add if any more related to this)
- Also they want among the 7 centers, student know about which center is less busy so he/she can send their request to there.

- you can add any functionality if anything i forgot and it can be usefull in this system. free to add any functionality or feature



tech stack : 
    frontend : react js
    backend : c# dotnet web api
    database : sql server




read requirements.txt and build complete end to end webapplication for given requirements, you can add anything if anything i forgot and it can be more usefull

database schma has been designed, you can 